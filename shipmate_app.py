import sys
import subprocess
import paramiko
import os
from PyQt6.QtWidgets import (
    QApplication, QWidget, QPushButton, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QComboBox,
    QTextEdit, QMessageBox, QScrollArea, QFrame, QStackedWidget, QCompleter, QTabWidget,
    QLayout, QLayoutItem, QSizePolicy, QWidgetItem
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QStringListModel, QRect, QSize, QPoint
from PyQt6.QtGui import QFont, QPainter, QPen, QColor, QPixmap


class FlowLayout(QLayout):
    """A layout that arranges widgets in a flow, wrapping to new lines as needed"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._item_list = []
        self._spacing = 6

    def addItem(self, item):
        self._item_list.append(item)

    def addWidget(self, widget):
        """Add a widget to the layout"""
        self.addItem(QWidgetItem(widget))

    def count(self):
        return len(self._item_list)

    def itemAt(self, index):
        if 0 <= index < len(self._item_list):
            return self._item_list[index]
        return None

    def takeAt(self, index):
        if 0 <= index < len(self._item_list):
            item = self._item_list.pop(index)
            return item
        return None

    def clear(self):
        """Clear all items from the layout"""
        while self._item_list:
            item = self.takeAt(0)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)

    def expandingDirections(self):
        return Qt.Orientation(0)

    def hasHeightForWidth(self):
        return True

    def heightForWidth(self, width):
        height = self._do_layout(QRect(0, 0, width, 0), True)
        return height

    def setGeometry(self, rect):
        super().setGeometry(rect)
        self._do_layout(rect, False)

    def invalidate(self):
        """Invalidate the layout"""
        super().invalidate()
        self.update()

    def sizeHint(self):
        return self.minimumSize()

    def minimumSize(self):
        size = QSize()
        for item in self._item_list:
            size = size.expandedTo(item.minimumSize())
        size += QSize(2 * self.contentsMargins().left(), 2 * self.contentsMargins().top())
        return size

    def setSpacing(self, spacing):
        self._spacing = spacing

    def spacing(self):
        return self._spacing

    def _do_layout(self, rect, test_only):
        x = rect.x()
        y = rect.y()
        line_height = 0

        for item in self._item_list:
            widget = item.widget()
            if widget is None:
                continue

            space_x = self.spacing()
            space_y = self.spacing()

            next_x = x + item.sizeHint().width() + space_x
            if next_x - space_x > rect.right() and line_height > 0:
                x = rect.x()
                y = y + line_height + space_y
                next_x = x + item.sizeHint().width() + space_x
                line_height = 0

            if not test_only:
                item.setGeometry(QRect(QPoint(x, y), item.sizeHint()))

            x = next_x
            line_height = max(line_height, item.sizeHint().height())

        return y + line_height - rect.y()


class CustomCheckBox(QWidget):
    """Custom checkbox widget with full control over appearance"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.checked = False
        self.hovered = False
        self.setFixedSize(20, 20)
        self.setStyleSheet("background-color: transparent;")
        self.setCursor(Qt.CursorShape.PointingHandCursor)

    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.checked = not self.checked
            self.update()
            # Emit a signal similar to QCheckBox
            if hasattr(self, '_state_changed_callback'):
                state = Qt.CheckState.Checked.value if self.checked else Qt.CheckState.Unchecked.value
                self._state_changed_callback(state)

    def enterEvent(self, _event):
        self.hovered = True
        self.update()

    def leaveEvent(self, _event):
        self.hovered = False
        self.update()

    def paintEvent(self, _event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Define colors for different states (dark theme)
        if self.checked:
            if self.hovered:
                border_color = QColor("#4CAF50")  # Green on hover
                fill_color = QColor("#4CAF50")
            else:
                border_color = QColor("#2196F3")  # Blue border when checked
                fill_color = QColor("#2196F3")
        else:
            if self.hovered:
                border_color = QColor("#666666")  # Gray on hover
                fill_color = QColor("transparent")
            else:
                border_color = QColor("#444444")  # Dark gray border
                fill_color = QColor("transparent")

        # Draw checkbox square with rounded corners
        rect = self.rect().adjusted(2, 2, -2, -2)

        # Fill background if checked
        if self.checked:
            painter.setBrush(fill_color)
            painter.setPen(QPen(border_color, 2))
            painter.drawRoundedRect(rect, 3, 3)
        else:
            painter.setBrush(QColor("transparent"))
            painter.setPen(QPen(border_color, 2))
            painter.drawRoundedRect(rect, 3, 3)

        # Draw checkmark if checked
        if self.checked:
            painter.setPen(QPen(QColor("white"), 2.5))

            # Draw a centered checkmark
            center_x = rect.center().x()
            center_y = rect.center().y()

            # Better centered checkmark coordinates
            painter.drawLine(
                center_x - 3, center_y,
                center_x, center_y + 3
            )
            painter.drawLine(
                center_x, center_y + 3,
                center_x + 4, center_y - 2
            )

    def setChecked(self, checked):
        """Set the checked state"""
        if self.checked != checked:
            self.checked = checked
            self.update()

    def isChecked(self):
        """Get the checked state"""
        return self.checked

    def stateChanged(self, callback):
        """Connect a callback for state changes (similar to QCheckBox.stateChanged.connect)"""
        self._state_changed_callback = callback

class LogReaderWorker(QThread):
    new_line = pyqtSignal(str)

    def __init__(self, filepath):
        super().__init__()
        self.filepath = filepath
        self._running = True

    def run(self):
        try:
            with open(self.filepath, 'r') as f:
                f.seek(0, os.SEEK_END)
                while self._running:
                    line = f.readline()
                    if line:
                        self.new_line.emit(line.strip())
                    else:
                        self.msleep(100)
        except Exception as e:
            self.new_line.emit(f"[LogReader error] {e}")

    def stop(self):
        self._running = False

class FrontPage(QWidget):
    """Front page with logo and search bar"""
    search_initiated = pyqtSignal(str)  # Signal to emit when search is started

    def __init__(self):
        super().__init__()
        self.hosts_file = "/etc/ansible/playbooks/abomination/shipmate-desktop/hosts"
        self.ship_data = []  # Store all ships and companies for dropdown
        self.update_timer = QTimer()  # Timer to prevent rapid updates
        self.update_timer.setSingleShot(True)
        self.update_timer.timeout.connect(self.delayed_update_completer)
        self.pending_text = ""
        self.load_ship_data()
        self.init_ui()

    def load_ship_data(self):
        """Load all ships and companies for dropdown search"""
        self.ship_data = []
        companies = set()

        try:
            with open(self.hosts_file, 'r') as f:
                for line in f:
                    parts = line.strip().split()
                    if not parts:
                        continue
                    ship = parts[0]

                    # Add ship name to data
                    self.ship_data.append(ship)

                    # Extract company name
                    if '-' in ship:
                        ship_parts = ship.split('-')
                        if len(ship_parts) >= 2:
                            if len(ship_parts) >= 3 and ship_parts[2] in ['shipping', 'marine', 'lines', 'group']:
                                company = '-'.join(ship_parts[:3]).title()
                            elif len(ship_parts) >= 2 and ship_parts[1] in ['american', 'eastern', 'western', 'northern', 'southern']:
                                company = '-'.join(ship_parts[:2]).title()
                            else:
                                company = ship_parts[0].title()
                        else:
                            company = ship_parts[0].title()
                    else:
                        company = "Other"

                    companies.add(company)

            # Add companies to the data
            self.ship_data.extend(sorted(companies))

        except Exception as e:
            print(f"Error loading ship data: {e}")

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setSpacing(30)

        # Add top spacer
        layout.addStretch(1)

        # Logo/Title section
        logo_layout = QVBoxLayout()
        logo_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Logo image
        logo_label = QLabel()
        logo_pixmap = QPixmap("orca_ai_logo.jpeg")
        if not logo_pixmap.isNull():
            # Scale the logo to a reasonable size (max 200px height, maintain aspect ratio)
            scaled_pixmap = logo_pixmap.scaled(300, 200, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            logo_label.setPixmap(scaled_pixmap)
            logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            logo_label.setStyleSheet("margin: 20px;")
            logo_layout.addWidget(logo_label)

        # Main title
        title_label = QLabel("ShipMate Desktop")
        title_label.setFont(QFont("Arial", 48, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            color: #2196F3;
            margin: 20px;
            font-weight: bold;
        """)
        logo_layout.addWidget(title_label)

        # Subtitle
        subtitle_label = QLabel("Orca AI Fleet Management System")
        subtitle_label.setFont(QFont("Arial", 18, QFont.Weight.Normal))
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("""
            color: #ffffff;
            margin-bottom: 40px;
            opacity: 0.8;
        """)
        logo_layout.addWidget(subtitle_label)

        layout.addLayout(logo_layout)

        # Search section
        search_layout = QVBoxLayout()
        search_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Search instruction
        search_instruction = QLabel("Search for ships or companies to get started")
        search_instruction.setFont(QFont("Arial", 14))
        search_instruction.setAlignment(Qt.AlignmentFlag.AlignCenter)
        search_instruction.setStyleSheet("color: #cccccc; margin-bottom: 15px;")
        search_layout.addWidget(search_instruction)

        # Search container with input and button - improved layout
        search_input_layout = QHBoxLayout()
        search_input_layout.setSpacing(12)
        search_input_layout.setContentsMargins(20, 0, 20, 0)  # Add margins to prevent cut-off

        # Search bar
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Type ship name or company...")
        self.search_input.setFont(QFont("Arial", 16))
        self.search_input.setMinimumHeight(50)
        self.search_input.setMaximumWidth(480)  # Slightly smaller to accommodate button
        self.search_input.setStyleSheet("""
            QLineEdit {
                background-color: #3c3c3c;
                border: none;
                border-radius: 25px;
                padding: 15px 25px;
                color: #ffffff;
                font-size: 16px;
            }
            QLineEdit:focus {
                background-color: #404040;
            }
        """)
        self.search_input.returnPressed.connect(self.on_search_enter)

        # Add dropdown completer with proper filtering
        self.completer = QCompleter(self.ship_data)
        self.completer.setCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.completer.setFilterMode(Qt.MatchFlag.MatchContains)
        self.completer.setCompletionMode(QCompleter.CompletionMode.PopupCompletion)
        self.completer.setMaxVisibleItems(10)

        # Fix the model to prevent breaking with longer text
        self.completer_model = QStringListModel(self.ship_data)
        self.completer.setModel(self.completer_model)

        # Style the completer popup with better positioning
        popup = self.completer.popup()
        popup.setStyleSheet("""
            QListView {
                background-color: #3c3c3c;
                border: 2px solid #555555;
                border-radius: 8px;
                color: #ffffff;
                font-size: 14px;
                padding: 5px;
                selection-background-color: #2196F3;
                outline: none;
            }
            QListView::item {
                padding: 10px 15px;
                border-radius: 4px;
                margin: 1px;
                min-height: 20px;
            }
            QListView::item:hover {
                background-color: #404040;
            }
            QListView::item:selected {
                background-color: #2196F3;
                color: #ffffff;
            }
        """)

        self.search_input.setCompleter(self.completer)
        self.completer.activated.connect(self.on_completion_selected)

        # Add text changed handler to update completer dynamically
        self.search_input.textChanged.connect(self.update_completer)

        # Fix dropdown positioning and width
        def update_completer_width():
            self.completer.popup().setMinimumWidth(self.search_input.width())

        # Connect to ensure dropdown width updates with input width
        self.search_input.resizeEvent = lambda event: (
            QLineEdit.resizeEvent(self.search_input, event),
            update_completer_width()
        )[1]

        search_input_layout.addWidget(self.search_input)

        # Search button - properly sized
        search_btn = QPushButton("Search")
        search_btn.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        search_btn.setMinimumHeight(50)
        search_btn.setMinimumWidth(100)
        search_btn.setMaximumWidth(120)
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                border: none;
                border-radius: 25px;
                padding: 12px 16px;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        search_btn.clicked.connect(self.on_search_enter)
        search_input_layout.addWidget(search_btn)

        search_layout.addLayout(search_input_layout)

        layout.addLayout(search_layout)

        # Add bottom spacer
        layout.addStretch(2)

        self.setLayout(layout)

    def update_completer(self, text):
        """Update completer with debouncing to prevent breaking"""
        self.pending_text = text
        self.update_timer.stop()
        self.update_timer.start(150)  # 150ms delay to prevent rapid updates

    def delayed_update_completer(self):
        """Actual completer update with filtering - ignoring hyphens"""
        text = self.pending_text
        try:
            # Prevent issues with very long text
            if len(text) > 50:
                return

            # Normalize search text - remove hyphens
            normalized_text = text.lower().strip().replace('-', '')

            if len(text) < 2:
                # Show limited items for short text
                self.completer_model.setStringList(self.ship_data[:25])
            elif len(text) > 20:
                # For very long text, be more restrictive
                filtered_items = []
                for item in self.ship_data:
                    # Normalize item by removing hyphens
                    normalized_item = item.lower().replace('-', '')
                    if normalized_item.startswith(normalized_text):
                        filtered_items.append(item)
                        if len(filtered_items) >= 5:
                            break
                self.completer_model.setStringList(filtered_items)
            else:
                # Normal filtering for reasonable length text
                filtered_items = []

                # First pass: exact starts-with matches (ignoring hyphens)
                for item in self.ship_data:
                    # Normalize item by removing hyphens
                    normalized_item = item.lower().replace('-', '')
                    if normalized_item.startswith(normalized_text):
                        filtered_items.append(item)
                        if len(filtered_items) >= 10:  # Limit early
                            break

                # Second pass: contains matches (if we need more items)
                if len(filtered_items) < 10:
                    for item in self.ship_data:
                        # Normalize item by removing hyphens
                        normalized_item = item.lower().replace('-', '')
                        if (normalized_text in normalized_item and
                            item not in filtered_items):
                            filtered_items.append(item)
                            if len(filtered_items) >= 10:
                                break

                self.completer_model.setStringList(filtered_items)

            # Only show popup if we have matches and text is reasonable length
            if self.completer_model.rowCount() > 0 and len(text) <= 30:
                self.completer.complete()

        except Exception as e:
            # Fallback to prevent crashes
            print(f"Completer error: {e}")
            self.completer_model.setStringList([])

    def on_completion_selected(self, text):
        """Handle when user selects an item from dropdown"""
        self.search_input.setText(text)
        self.search_initiated.emit(text)

    def on_search_enter(self):
        """Handle Enter key press in search - go to main page regardless of search text"""
        text = self.search_input.text().strip()
        # Emit search signal with text (empty or not) to go to main page
        self.search_initiated.emit(text)

class MainApp(QWidget):
    """Main application container that manages front page and main view"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("ShipMate Desktop")

        # Auto-adjust window size and position based on screen resolution
        self.auto_adjust_resolution()

        # Apply clean, fluid dark theme
        self.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLineEdit {
                background-color: #3c3c3c;
                border: none;
                border-radius: 12px;
                padding: 10px 16px;
                color: #ffffff;
                font-size: 13px;
                font-weight: normal;
            }
            QLineEdit:focus {
                background-color: #404040;
                outline: none;
            }
            QTextEdit {
                background-color: #1e1e1e;
                border: none;
                border-radius: 6px;
                color: #ffffff;
                font-family: 'Courier New', monospace;
                padding: 12px;
                font-size: 14px;
            }
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QLabel {
                color: #ffffff;
                background-color: transparent;
                font-weight: normal;
            }
            QPushButton {
                background-color: #404040;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                color: #ffffff;
                font-weight: 500;
                font-size: 12px;
                min-height: 32px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #4a4a4a;
                color: #2196F3;
            }
            QPushButton:pressed {
                background-color: #353535;
            }
        """)

        # Create stacked widget to switch between views
        self.stacked_widget = QStackedWidget()

        # Create front page
        self.front_page = FrontPage()
        self.front_page.search_initiated.connect(self.on_search_initiated)

        # Create main application view
        self.main_view = ShipMateMainView()

        # Add both views to stacked widget
        self.stacked_widget.addWidget(self.front_page)
        self.stacked_widget.addWidget(self.main_view)

        # Set layout
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.stacked_widget)
        self.setLayout(layout)

        # Start with front page
        self.stacked_widget.setCurrentWidget(self.front_page)

    def on_search_initiated(self, search_text):
        """Handle search from front page - go to main page regardless of search text"""
        # Always switch to main view
        self.stacked_widget.setCurrentWidget(self.main_view)

        # Set the search text and trigger search (even if empty)
        self.main_view.search_input.setText(search_text)
        self.main_view.filter_ships()

    def is_vessel_name(self, text):
        """Check if the text is a specific vessel name (not a company) - ignoring hyphens"""
        # Normalize search text by removing hyphens
        normalized_text = text.lower().replace('-', '').strip()

        # Load vessel names from hosts file
        try:
            with open("/etc/ansible/playbooks/abomination/shipmate-desktop/hosts", 'r') as f:
                for line in f:
                    parts = line.strip().split()
                    if parts:
                        # Normalize vessel name by removing hyphens
                        normalized_vessel = parts[0].lower().replace('-', '')
                        if normalized_vessel == normalized_text:
                            return True
        except Exception:
            pass
        return False

    def auto_adjust_resolution(self):
        """Automatically adjust window size and position based on screen resolution"""
        # Get the primary screen
        app = QApplication.instance()
        if app is None:
            # Fallback to default size if no QApplication instance
            self.setGeometry(100, 100, 1000, 600)
            return

        screen = app.primaryScreen()
        if screen is None:
            # Fallback to default size if no screen found
            self.setGeometry(100, 100, 1000, 600)
            return

        # Get screen geometry
        screen_geometry = screen.availableGeometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        # Calculate optimal window size (better proportions for the app)
        min_width, min_height = 1000, 700
        max_width, max_height = 1400, 1000

        optimal_width = max(min_width, min(max_width, int(screen_width * 0.75)))
        optimal_height = max(min_height, min(max_height, int(screen_height * 0.85)))

        # Center the window on screen
        x = (screen_width - optimal_width) // 2
        y = (screen_height - optimal_height) // 2

        # Ensure window doesn't go off-screen
        x = max(0, x)
        y = max(0, y)

        # Set the geometry
        self.setGeometry(x, y, optimal_width, optimal_height)

        # Set minimum size to ensure usability
        self.setMinimumSize(min_width, min_height)

        print(f"Screen resolution: {screen_width}x{screen_height}")
        print(f"Window size: {optimal_width}x{optimal_height} at position ({x}, {y})")

class ShipMateMainView(QWidget):
    """Main application view with ships, buttons, etc."""

    def __init__(self):
        super().__init__()

        self.hosts_file = "/etc/ansible/playbooks/abomination/shipmate-desktop/hosts"
        self.log_dir = "/etc/ansible/playbooks/abomination/shipmate-desktop/logs"
        os.makedirs(self.log_dir, exist_ok=True)

        self.checked_ships = set()
        self.log_thread = None
        self.init_ui()
        self.load_ships()
        self.checked_ships.clear()

        # Apply button styling after UI is fully initialized
        self.apply_button_styling()

    def go_back_to_home(self):
        """Signal to go back to home page"""
        try:
            # Find the parent MainApp and switch to front page
            parent = self.parent()
            while parent and not isinstance(parent, MainApp):
                parent = parent.parent()
            if parent:
                # Clear any search filters before going back
                self.search_input.clear()
                self.reset_all_visibility()

                # Clear all selections
                self.checked_ships.clear()
                self.update_selected_vessels_display()

                # Switch to front page
                parent.stacked_widget.setCurrentWidget(parent.front_page)
                parent.front_page.search_input.clear()  # Clear search on return

                print("Successfully returned to front page")
            else:
                print("Error: Could not find parent MainApp")
        except Exception as e:
            print(f"Error in go_back_to_home: {e}")
            # Fallback - try to clear current state at least
            try:
                self.search_input.clear()
                self.checked_ships.clear()
                self.update_selected_vessels_display()
            except:
                pass

    def resizeEvent(self, event):
        """Handle window resize events to maintain proper layout"""
        super().resizeEvent(event)
        # Automatically reapply button styling when window is resized
        if hasattr(self, 'deploy_btn'):  # Check if UI is initialized
            self.apply_button_styling()
        # Fix vessel pills scaling after resize
        if hasattr(self, 'vessel_pills'):
            QTimer.singleShot(150, self.fix_vessel_pills_scaling)
        # Update vessel list layout for better responsiveness
        if hasattr(self, 'scroll_widget'):
            QTimer.singleShot(200, self.adjust_vessel_list_layout)

    def get_screen_info(self):
        """Get detailed screen information for debugging"""
        app = QApplication.instance()
        if app is None:
            return "No QApplication instance"

        screen = app.primaryScreen()
        if screen is None:
            return "No screen found"

        geometry = screen.geometry()
        available = screen.availableGeometry()

        info = f"""Screen Information:
        Full geometry: {geometry.width()}x{geometry.height()}
        Available geometry: {available.width()}x{available.height()}
        Device pixel ratio: {screen.devicePixelRatio()}
        Physical DPI: {screen.physicalDotsPerInch()}
        Logical DPI: {screen.logicalDotsPerInch()}"""

        return info

    def center_on_screen(self):
        """Center the window on the current screen"""
        app = QApplication.instance()
        if app is None:
            return

        screen = app.primaryScreen()
        if screen is None:
            return

        screen_geometry = screen.availableGeometry()
        window_geometry = self.geometry()

        x = (screen_geometry.width() - window_geometry.width()) // 2
        y = (screen_geometry.height() - window_geometry.height()) // 2

        self.move(x, y)

    def show_screen_info(self):
        """Show screen information in a message box"""
        info = self.get_screen_info()
        current_geometry = self.geometry()
        window_info = f"""
Current Window:
Position: ({current_geometry.x()}, {current_geometry.y()})
Size: {current_geometry.width()}x{current_geometry.height()}

{info}"""

        QMessageBox.information(self, "Screen Information", window_info)

    def apply_button_styling(self):
        """Apply consistent styling and sizing to all buttons"""
        window_width = self.width()

        # Calculate button width based on window width and number of buttons per row
        # Assuming roughly 11 buttons in main row, with some margin
        available_width = window_width - 100  # Leave margin
        button_width = max(120, min(180, available_width // 11))
        button_height = 40  # Fixed height that looks good

        # Larger font size for better readability
        if window_width < 1200:
            font_size = 11
        elif window_width < 1600:
            font_size = 12
        else:
            font_size = 13

        # Update the stylesheet with better sizing - only for action buttons, not expand buttons
        button_style = f"""
            QPushButton {{
                background-color: #404040;
                border: none;
                border-radius: 5px;
                padding: 6px 12px;
                color: #ffffff;
                font-weight: bold;
                font-size: {font_size}px;
                min-width: {button_width}px;
                min-height: {button_height}px;
                max-width: {button_width + 20}px;
                max-height: {button_height + 5}px;
                margin: 2px;
            }}
            QPushButton:hover {{
                background-color: #4a4a4a;
            }}
            QPushButton:pressed {{
                background-color: #353535;
            }}
        """

        # Apply only to main action buttons, not the small expand buttons
        action_buttons = [
            self.deploy_btn, self.restart_btn, self.sanity_btn, self.screenshot_btn,
            self.ping_ships_btn, self.screenshot_day_btn, self.screenshot_thermal_btn,
            self.ping_cameras_btn, self.ping_vsat_btn, self.add_ping_slack_btn,
            self.remove_ping_slack_btn, self.pull_new_ships_btn
        ]

        for button in action_buttons:
            if button:  # Check if button exists
                button.setStyleSheet(button_style)
                button.setMinimumSize(button_width, button_height)
                button.setMaximumSize(button_width + 20, button_height + 5)

    def init_ui(self):
        # Apply beautiful borderless styling for expand/collapse buttons
        self.setStyleSheet("""
            /* Beautiful borderless expand/collapse buttons */
            QFrame QPushButton {
                background-color: #404040;
                border: none;
                border-radius: 6px;
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                min-width: 30px;
                max-width: 34px;
                min-height: 30px;
                max-height: 34px;
                padding: 3px;
            }
            QFrame QPushButton:hover {
                background-color: #4a4a4a;
                color: #2196F3;
            }
        """)

        layout = QVBoxLayout()

        # Compact header with back button and title
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(8, 5, 8, 2)

        # Fixed back button - wider to prevent text cut-off
        self.back_btn = QPushButton("← Back to Home")
        self.back_btn.setMinimumWidth(160)
        self.back_btn.setMaximumWidth(200)
        self.back_btn.setMinimumHeight(40)
        self.back_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                border: 1px solid #1976D2;
                border-radius: 8px;
                padding: 8px 16px;
                color: #ffffff;
                font-weight: bold;
                font-size: 11px;
                text-align: center;
                min-height: 40px;
                min-width: 160px;
            }
            QPushButton:hover {
                background-color: #1976D2;
                border-color: #1565C0;
                color: #ffffff;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        self.back_btn.clicked.connect(self.go_back_to_home)
        self.back_btn.setToolTip("Return to the front page")
        header_layout.addWidget(self.back_btn)

        # Add stretch to push title to center
        header_layout.addStretch()

        # Compact title in header - centered
        header_title = QLabel("ShipMate Desktop")
        header_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        header_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_title.setStyleSheet("""
            color: #2196F3;
            margin: 0px 15px;
            font-weight: bold;
        """)
        header_layout.addWidget(header_title)

        # Add stretch to balance the layout
        header_layout.addStretch()

        # Total vessel count in top right with green highlighting
        self.total_vessels_label = QLabel("Total: 0 vessels")
        self.total_vessels_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        self.total_vessels_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.total_vessels_label.setStyleSheet("""
            color: #4CAF50;
            margin: 0px 15px;
            font-weight: bold;
            background-color: rgba(76, 175, 80, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            border: none;
        """)
        header_layout.addWidget(self.total_vessels_label)

        layout.addLayout(header_layout)

        # Minimal spacing
        layout.addSpacing(5)

        # Compact search section
        search_container = QVBoxLayout()
        search_container.setContentsMargins(15, 5, 15, 5)

        # Compact search bar
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search ships or companies...")
        self.search_input.setFont(QFont("Segoe UI", 13))
        self.search_input.setMinimumHeight(35)
        self.search_input.setStyleSheet("""
            QLineEdit {
                background-color: #3c3c3c;
                border: none;
                border-radius: 18px;
                padding: 10px 18px;
                color: #ffffff;
                font-size: 13px;
            }
            QLineEdit:focus {
                background-color: #404040;
            }
        """)
        self.search_input.textChanged.connect(self.filter_ships)
        self.search_input.returnPressed.connect(self.filter_ships)  # Enable Enter key
        search_container.addWidget(self.search_input)

        layout.addLayout(search_container)
        layout.addSpacing(8)

        # Clean selected vessels section
        self.selected_container = QWidget()
        self.selected_container.setStyleSheet("""
            QWidget {
                background-color: #1e1e1e;
                border: none;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        selected_main_layout = QVBoxLayout(self.selected_container)
        selected_main_layout.setContentsMargins(8, 8, 8, 8)
        selected_main_layout.setSpacing(6)

        # Header with count and uncheck all button
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)

        self.selected_label = QLabel("Selected: 0 ships")
        self.selected_label.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        self.selected_label.setStyleSheet("""
            color: #4CAF50;
            font-weight: bold;
        """)
        header_layout.addWidget(self.selected_label)

        header_layout.addStretch()

        # Uncheck all button - wider to prevent text cut-off
        self.uncheck_all_btn = QPushButton("✕ Uncheck All")
        self.uncheck_all_btn.setMinimumWidth(120)
        self.uncheck_all_btn.setMaximumWidth(150)
        self.uncheck_all_btn.setMinimumHeight(35)
        self.uncheck_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                border: 1px solid #b71c1c;
                border-radius: 8px;
                padding: 6px 12px;
                color: #ffffff;
                font-weight: bold;
                font-size: 11px;
                text-align: center;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #b71c1c;
                border-color: #8d1e1e;
            }
            QPushButton:pressed {
                background-color: #8d1e1e;
            }
        """)
        self.uncheck_all_btn.clicked.connect(self.uncheck_all_vessels)
        self.uncheck_all_btn.setToolTip("Uncheck all selected vessels")
        header_layout.addWidget(self.uncheck_all_btn)

        selected_main_layout.addLayout(header_layout)

        # Create scrollable area for vessel pills
        self.selected_scroll_area = QScrollArea()
        self.selected_scroll_area.setWidgetResizable(True)
        self.selected_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.selected_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.selected_scroll_area.setMaximumHeight(200)  # Limit height to prevent taking too much space
        self.selected_scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #3c3c3c;
                width: 14px;
                border-radius: 7px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 7px;
                min-height: 30px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #2196F3;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """)

        self.selected_widget = QWidget()
        # Use a simple vertical layout with horizontal rows
        self.selected_layout = QVBoxLayout(self.selected_widget)
        self.selected_layout.setContentsMargins(5, 5, 5, 5)
        self.selected_layout.setSpacing(4)
        self.selected_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        self.selected_scroll_area.setWidget(self.selected_widget)

        # Store reference to pills for scaling
        self.vessel_pills = []

        # Flag to prevent recursive checkbox updates
        self._updating_checkboxes = False

        selected_main_layout.addWidget(self.selected_scroll_area)

        self.selected_container.hide()  # Initially hidden
        layout.addWidget(self.selected_container)

        layout.addSpacing(8)

        # Compact ship list header
        ships_label = QLabel("Ships / Companies")
        ships_label.setFont(QFont("Arial", 13, QFont.Weight.Bold))
        ships_label.setStyleSheet("""
            color: #2196F3;
            margin: 5px 15px 3px 15px;
            font-weight: bold;
        """)
        layout.addWidget(ships_label)

        # Enhanced scroll area - much larger to fill space above buttons
        self.scroll_area = QScrollArea()
        self.scroll_area.setMinimumHeight(600)  # Increased size to fill more space
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #2b2b2b;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #3c3c3c;
                width: 14px;
                border-radius: 7px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 7px;
                min-height: 30px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #2196F3;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """)
        self.scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_widget)
        self.scroll_layout.setContentsMargins(10, 5, 10, 5)
        self.scroll_layout.setSpacing(3)
        self.scroll_area.setWidget(self.scroll_widget)
        self.scroll_area.setWidgetResizable(True)
        layout.addWidget(self.scroll_area)

        # Store company widgets and ship checkboxes
        self.company_widgets = []
        self.ship_checkboxes = {}

        # Compact version selector
        version_container = QHBoxLayout()
        version_container.setContentsMargins(15, 8, 15, 8)

        version_label = QLabel("Version:")
        version_label.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        version_label.setStyleSheet("""
            color: #ffffff;
            margin-right: 8px;
            font-weight: bold;
        """)
        version_container.addWidget(version_label)

        # Create version combo with custom arrow overlay
        version_combo_widget = QWidget()
        version_combo_widget.setFixedWidth(120)
        version_combo_layout = QHBoxLayout(version_combo_widget)
        version_combo_layout.setContentsMargins(0, 0, 0, 0)
        version_combo_layout.setSpacing(0)

        self.version_combo = QComboBox()
        self.version_combo.addItems(["v3.14.0", "v3.13.0", "v3.10.1", "v3.7.3"])
        self.version_combo.setFixedWidth(120)
        self.version_combo.setStyleSheet("""
            QComboBox {
                background-color: #3c3c3c;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                padding-right: 25px;
                color: #ffffff;
                font-size: 11px;
                min-height: 20px;
            }
            QComboBox:hover {
                background-color: #404040;
            }
            QComboBox::drop-down {
                border: none;
                width: 0px;
            }
            QComboBox::down-arrow {
                image: none;
                width: 0px;
                height: 0px;
            }
            QComboBox QAbstractItemView {
                background-color: #3c3c3c;
                border: 1px solid #555555;
                border-radius: 4px;
                color: #ffffff;
                selection-background-color: #2196F3;
                outline: none;
            }
        """)

        # Create arrow label overlay
        arrow_label = QLabel("▼")
        arrow_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 10px;
                font-weight: bold;
                background-color: transparent;
                border: none;
                padding: 0px;
                margin: 0px;
            }
        """)
        arrow_label.setFixedSize(20, 20)
        arrow_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        arrow_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)

        version_combo_layout.addWidget(self.version_combo)

        # Position arrow label over the combo box
        arrow_label.setParent(version_combo_widget)
        arrow_label.move(95, 2)  # Position it on the right side
        version_container.addWidget(version_combo_widget)
        version_container.addStretch()

        layout.addLayout(version_container)
        layout.addSpacing(5)

        # Create fluid tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                background-color: #2b2b2b;
                border: none;
                border-radius: 0px;
            }
            QTabBar::tab {
                background-color: #3c3c3c;
                color: #ffffff;
                padding: 10px 18px;
                margin-right: 1px;
                border-radius: 6px 6px 0px 0px;
                font-size: 12px;
                font-weight: 500;
                min-width: 80px;
            }
            QTabBar::tab:selected {
                background-color: #2196F3;
                color: #ffffff;
            }
            QTabBar::tab:hover:!selected {
                background-color: #404040;
            }
        """)

        # Connect tab change signal to update close all tabs button visibility
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

        # Ships tab (main content) - clean layout without background
        ships_tab = QWidget()
        ships_tab.setStyleSheet("QWidget { background-color: transparent; }")
        ships_layout = QVBoxLayout(ships_tab)
        ships_layout.setContentsMargins(0, 0, 0, 0)

        # Add scroll area to take maximum space
        ships_layout.addWidget(self.scroll_area, 10)  # High stretch factor for maximum space

        # Add main ships tab
        self.tab_widget.addTab(ships_tab, "🚢 Ships & Actions")

        # Store active log tabs for cleanup
        self.active_log_tabs = {}

        # Create container for tab widget and close all button
        tab_container = QHBoxLayout()
        tab_container.setContentsMargins(0, 0, 0, 0)
        tab_container.setSpacing(8)

        # Add tab widget
        tab_container.addWidget(self.tab_widget)

        # Create "Close All Tabs" button (initially hidden)
        self.close_all_tabs_btn = QPushButton("🗂️ Close All Tabs")
        self.close_all_tabs_btn.setMinimumWidth(160)
        self.close_all_tabs_btn.setMaximumWidth(180)
        self.close_all_tabs_btn.setMinimumHeight(36)
        self.close_all_tabs_btn.setStyleSheet("""
            QPushButton {
                background-color: #795548;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                color: #ffffff;
                font-weight: 500;
                font-size: 12px;
                min-height: 36px;
                min-width: 160px;
            }
            QPushButton:hover {
                background-color: #8d6e63;
                color: #ffffff;
            }
            QPushButton:pressed {
                background-color: #6d4c41;
            }
        """)
        self.close_all_tabs_btn.clicked.connect(self.close_all_tabs)
        self.close_all_tabs_btn.hide()  # Initially hidden
        tab_container.addWidget(self.close_all_tabs_btn)

        layout.addLayout(tab_container)

        # Create action buttons at bottom - always visible, no margins to edge
        self.btn_container = QWidget()
        self.btn_container.setStyleSheet("QWidget { background-color: #2b2b2b; border: none; }")
        btn_layout = QHBoxLayout(self.btn_container)
        btn_layout.setContentsMargins(0, 4, 0, 4)  # No left/right margins, minimal top/bottom
        btn_layout.setSpacing(6)

        # All buttons in single row
        self.deploy_btn = QPushButton("🚀 Deploy")
        self.deploy_btn.clicked.connect(self.deploy)
        btn_layout.addWidget(self.deploy_btn)

        self.restart_btn = QPushButton("🔁 Restart Dockers")
        self.restart_btn.clicked.connect(lambda: self.run_action("restart_dockers.sh", "Restart Dockers"))
        btn_layout.addWidget(self.restart_btn)

        self.sanity_btn = QPushButton("🧪 Sanity Test")
        self.sanity_btn.clicked.connect(lambda: self.run_action("run_sanity.sh", "Sanity Test"))
        btn_layout.addWidget(self.sanity_btn)

        self.screenshot_btn = QPushButton("📸 Screenshot")
        self.screenshot_btn.clicked.connect(lambda: self.run_action("pull_photos.sh", "Screenshot"))
        btn_layout.addWidget(self.screenshot_btn)

        self.ping_ships_btn = QPushButton("📶 Ping Ships")
        self.ping_ships_btn.clicked.connect(lambda: self.run_action("ping_hosts.sh", "Ping Ships"))
        btn_layout.addWidget(self.ping_ships_btn)

        self.screenshot_day_btn = QPushButton("🌞 Screenshot Day")
        self.screenshot_day_btn.clicked.connect(lambda: self.run_action("pull_day_pano.sh", "Screenshot Day"))
        btn_layout.addWidget(self.screenshot_day_btn)

        self.screenshot_thermal_btn = QPushButton("🌡 Screenshot Thermal")
        self.screenshot_thermal_btn.clicked.connect(lambda: self.run_action("pull_thermal_pano.sh", "Screenshot Thermal"))
        btn_layout.addWidget(self.screenshot_thermal_btn)

        self.ping_cameras_btn = QPushButton("📷 Ping Cameras")
        self.ping_cameras_btn.clicked.connect(lambda: self.run_action("check_camera_ping.sh", "Ping Cameras"))
        btn_layout.addWidget(self.ping_cameras_btn)

        self.ping_vsat_btn = QPushButton("📡 Ping Vsat")
        self.ping_vsat_btn.clicked.connect(lambda: self.run_action("check_vsat.py", "Ping Vsat"))
        btn_layout.addWidget(self.ping_vsat_btn)

        self.add_ping_slack_btn = QPushButton("➕ Add Ping Slack")
        self.add_ping_slack_btn.clicked.connect(lambda: self.run_action("add_to_host_ping_ansible.sh", "Add Ping Slack"))
        btn_layout.addWidget(self.add_ping_slack_btn)

        self.remove_ping_slack_btn = QPushButton("➖ Remove Ping Slack")
        self.remove_ping_slack_btn.clicked.connect(lambda: self.run_action("remove_from_hosts_ping_ansible.sh", "Remove Ping Slack"))
        btn_layout.addWidget(self.remove_ping_slack_btn)

        self.pull_new_ships_btn = QPushButton("🆕 Pull New Vessels")
        self.pull_new_ships_btn.clicked.connect(lambda: self.run_action("pull_new_ships.sh", "Pull New Vessels", require_selection=False))
        btn_layout.addWidget(self.pull_new_ships_btn)

        # Add button container to bottom of main layout (always visible)
        layout.addWidget(self.btn_container)

        self.setLayout(layout)

    def get_simple_pill_style(self):
        """Get simple, consistent pill styling"""
        return {
            'font_size': 9,
            'border_radius': 12,
            'padding': 8,
            'height': 26
        }

    def fix_vessel_pills_scaling(self):
        """Update vessel pills layout when window resizes"""
        if not hasattr(self, 'vessel_pills') or not self.vessel_pills:
            return

        # Rebuild the display to adjust for new window size
        self.update_selected_vessels_display()

    def adjust_vessel_list_layout(self):
        """Adjust vessel list layout based on window size for better responsiveness"""
        try:
            window_width = self.width()

            # Adjust company frame styling based on window width
            for company_frame in self.company_widgets:
                if hasattr(company_frame, 'company_name'):
                    # Adjust font sizes and spacing based on window width
                    if window_width < 1000:
                        # Compact layout for smaller windows
                        font_size = 11
                        padding = 6
                        spacing = 2
                    elif window_width < 1300:
                        # Medium layout
                        font_size = 12
                        padding = 8
                        spacing = 3
                    else:
                        # Full layout for larger windows
                        font_size = 13
                        padding = 8
                        spacing = 4

                    # Update company label if it exists
                    for child in company_frame.findChildren(QLabel):
                        if "ships)" in child.text():  # This is the company label
                            child.setFont(QFont("Arial", font_size, QFont.Weight.Bold))
                            break

                    # Update layout margins
                    layout = company_frame.layout()
                    if layout:
                        layout.setContentsMargins(padding, spacing, padding, spacing)
                        layout.setSpacing(spacing)

        except Exception as e:
            print(f"Error in adjust_vessel_list_layout: {e}")

    def update_selected_vessels_display(self):
        """Update the display of selected vessels as blue pills with X buttons"""
        # Clear existing selected vessels display and pill references
        for i in reversed(range(self.selected_layout.count())):
            item = self.selected_layout.itemAt(i)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)

        # Clear pill references
        self.vessel_pills.clear()

        # Update count and show/hide the selected vessels section
        count = len(self.checked_ships)
        if count > 0:
            self.selected_label.setText(f"Selected: {count} ships")
            self.selected_container.show()

            # Get simple, consistent styling
            style = self.get_simple_pill_style()

            # Create rows of pills that wrap - better scaling calculation
            available_width = self.width() - 120  # Account for margins and scrollbar
            estimated_pill_width = 120  # Average pill width including spacing
            pills_per_row = max(1, available_width // estimated_pill_width)
            current_row = None
            current_row_count = 0

            for i, ship_name in enumerate(sorted(self.checked_ships)):
                # Create new row if needed
                if current_row is None or current_row_count >= pills_per_row:
                    current_row = QWidget()
                    row_layout = QHBoxLayout(current_row)
                    row_layout.setContentsMargins(0, 2, 0, 2)  # Small vertical margins
                    row_layout.setSpacing(8)  # Better spacing between pills
                    row_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
                    self.selected_layout.addWidget(current_row)
                    current_row_count = 0

                # Create simple, clean pill widget
                pill_widget = QWidget()
                pill_widget.setFixedHeight(style['height'])
                pill_widget.setMinimumWidth(100)  # Ensure minimum width for better scaling
                pill_widget.setStyleSheet(f"""
                    QWidget {{
                        background-color: #2196F3;
                        border-radius: {style['border_radius']}px;
                        border: none;
                    }}
                    QWidget:hover {{
                        background-color: #1976D2;
                    }}
                """)
                pill_layout = QHBoxLayout(pill_widget)
                pill_layout.setContentsMargins(style['padding'], 0, 4, 0)  # Better vertical centering
                pill_layout.setSpacing(6)
                pill_layout.setAlignment(Qt.AlignmentFlag.AlignVCenter)  # Center content vertically

                # Simple vessel name label
                vessel_label = QLabel(ship_name)
                vessel_label.setFont(QFont("Arial", style['font_size']))
                vessel_label.setStyleSheet("""
                    QLabel {
                        color: #ffffff;
                        background-color: transparent;
                        border: none;
                        padding: 0px;
                        margin: 0px;
                    }
                """)
                pill_layout.addWidget(vessel_label)

                # Bigger red X button without background
                remove_btn = QPushButton("✕")
                btn_size = 20  # Bigger button
                remove_btn.setFixedSize(btn_size, btn_size)
                remove_btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: transparent;
                        border: none;
                        color: #ff4444;
                        font-size: 14px;
                        font-weight: bold;
                        padding: 0px;
                        margin: 0px;
                    }}
                    QPushButton:hover {{
                        color: #ff0000;
                        background-color: transparent;
                    }}
                    QPushButton:pressed {{
                        color: #cc0000;
                        background-color: transparent;
                    }}
                """)
                remove_btn.clicked.connect(lambda _checked, name=ship_name: self.remove_selected_vessel(name))
                pill_layout.addWidget(remove_btn)

                # Store pill reference
                self.vessel_pills.append({
                    'widget': pill_widget,
                    'label': vessel_label,
                    'button': remove_btn,
                    'ship_name': ship_name
                })

                # Add pill to current row
                row_layout.addWidget(pill_widget)
                current_row_count += 1

            # Add stretch to last row to align pills to left
            if current_row is not None:
                row_layout.addStretch()
        else:
            self.selected_container.hide()

    def remove_selected_vessel(self, ship_name):
        """Remove a vessel from selection"""
        if ship_name in self.checked_ships:
            # Uncheck the vessel
            if ship_name in self.ship_checkboxes:
                checkbox = self.ship_checkboxes[ship_name]['checkbox']
                checkbox.setChecked(False)

            # Remove from checked ships set
            self.checked_ships.discard(ship_name)

            # Update the display
            self.update_selected_vessels_display()

    def uncheck_all_vessels(self):
        """Uncheck all selected vessels"""
        # Prevent recursive updates
        if self._updating_checkboxes:
            return

        try:
            self._updating_checkboxes = True
            print(f"Unchecking all vessels. Currently selected: {len(self.checked_ships)}")

            # First, uncheck all individual ship checkboxes
            for ship_name in list(self.checked_ships):
                if ship_name in self.ship_checkboxes:
                    checkbox = self.ship_checkboxes[ship_name]['checkbox']
                    checkbox.setChecked(False)
                    print(f"Unchecked ship: {ship_name}")

            # Clear the checked ships set
            self.checked_ships.clear()

            # Uncheck all company checkboxes
            for company_frame in self.company_widgets:
                if hasattr(company_frame, 'company_checkbox'):
                    company_frame.company_checkbox.setChecked(False)
                    print(f"Unchecked company: {getattr(company_frame, 'company_name', 'Unknown')}")

            # Update the display
            self.update_selected_vessels_display()

            print("Successfully unchecked all vessels")

        except Exception as e:
            print(f"Error in uncheck_all_vessels: {e}")
            # Fallback - force clear everything
            try:
                self.checked_ships.clear()
                self.update_selected_vessels_display()
            except:
                pass
        finally:
            self._updating_checkboxes = False

    def create_log_tab(self, action_name):
        """Create a new tab for logging a specific action"""
        # Create new tab widget
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)

        # Create log output area for this specific action
        log_output = QTextEdit()
        log_output.setReadOnly(True)
        log_layout.addWidget(log_output)

        # Button container for tab controls - improved spacing
        btn_container = QHBoxLayout()
        btn_container.setSpacing(15)
        btn_container.setContentsMargins(12, 12, 12, 12)

        # Add kill script button - properly sized
        kill_btn = QPushButton("⚠️ Kill Script")
        kill_btn.setMinimumWidth(140)
        kill_btn.setMaximumWidth(160)
        kill_btn.setMinimumHeight(36)
        kill_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff5722;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                min-width: 140px;
                min-height: 36px;
            }
            QPushButton:hover {
                background-color: #e64a19;
            }
        """)
        kill_btn.clicked.connect(lambda: self.kill_script(action_name))
        btn_container.addWidget(kill_btn)

        # Add close button for the tab - properly sized
        close_btn = QPushButton("❌ Close Tab")
        close_btn.setMinimumWidth(140)
        close_btn.setMaximumWidth(160)
        close_btn.setMinimumHeight(36)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                min-width: 140px;
                min-height: 36px;
            }
            QPushButton:hover {
                background-color: #b71c1c;
            }
        """)
        close_btn.clicked.connect(lambda: self.close_log_tab(action_name))
        btn_container.addWidget(close_btn)

        log_layout.addLayout(btn_container)

        # Add tab to tab widget with running indicator
        tab_index = self.tab_widget.addTab(log_tab, f"� {action_name}")

        # Store reference to log output, tab, and process info
        self.active_log_tabs[action_name] = {
            'log_output': log_output,
            'tab_widget': log_tab,
            'tab_index': tab_index,
            'process': None,  # Will store the subprocess.Popen object
            'script_name': None  # Will store the script name for killing
        }

        # Don't automatically switch to the new tab - let user choose

        # Update button visibility based on current tab
        self.update_close_all_tabs_visibility()

        return log_output

    def on_tab_changed(self, _index):
        """Handle tab change to show/hide close all tabs button"""
        self.update_close_all_tabs_visibility()

    def update_close_all_tabs_visibility(self):
        """Update visibility of close all tabs button based on current tab and active tabs"""
        # Check if attributes are initialized
        if not hasattr(self, 'active_log_tabs') or not hasattr(self, 'close_all_tabs_btn'):
            return

        current_index = self.tab_widget.currentIndex()

        # Show button only if:
        # 1. There are active log tabs
        # 2. Current tab is NOT the first tab (Ships & Actions)
        if self.active_log_tabs and current_index > 0:
            self.close_all_tabs_btn.show()
        else:
            self.close_all_tabs_btn.hide()

    def kill_script(self, action_name):
        """Kill the running script for a specific action"""
        if action_name in self.active_log_tabs:
            tab_info = self.active_log_tabs[action_name]
            log_output = tab_info['log_output']
            killed = False

            try:
                # First try to kill the stored process if available
                process = tab_info.get('process')
                if process and process.poll() is None:  # Process is still running
                    process.terminate()
                    killed = True

                    # Give it a moment, then force kill if needed
                    import time
                    time.sleep(1)
                    if process.poll() is None:
                        process.kill()

                # Also try to kill using pkill with script name as backup
                script_name = tab_info.get('script_name')
                if script_name:
                    import subprocess
                    result = subprocess.run(['pkill', '-f', script_name], capture_output=True, text=True)
                    if result.returncode == 0:
                        killed = True

                # Show simple success or failure message
                if killed:
                    log_output.append("🔴 Script terminated successfully")
                else:
                    log_output.append("🔴 No running script found to terminate")

            except Exception:
                log_output.append("🔴 Failed to terminate script")

    def close_all_tabs(self):
        """Close all open log tabs"""
        # Get a list of all tab names to avoid modifying dict during iteration
        tab_names = list(self.active_log_tabs.keys())

        for action_name in tab_names:
            self.close_log_tab(action_name)

        # Update button visibility (will be hidden since no tabs remain)
        self.update_close_all_tabs_visibility()

    def close_log_tab(self, action_name):
        """Close a specific log tab"""
        if action_name in self.active_log_tabs:
            tab_info = self.active_log_tabs[action_name]

            # Kill the script first if still running
            self.kill_script(action_name)

            # Stop the log thread if it exists
            if 'log_thread' in tab_info:
                log_thread = tab_info['log_thread']
                log_thread.stop()
                log_thread.quit()
                log_thread.wait()

            # Remove the tab from the widget
            tab_index = self.tab_widget.indexOf(tab_info['tab_widget'])
            if tab_index != -1:
                self.tab_widget.removeTab(tab_index)

            # Clean up the reference
            del self.active_log_tabs[action_name]

            # Update button visibility
            self.update_close_all_tabs_visibility()

    def load_ships(self):
        # Clear existing widgets
        for widget in self.company_widgets:
            widget.setParent(None)
        self.company_widgets.clear()
        self.ship_checkboxes.clear()
        self.checked_ships.clear()

        companies = {}

        try:
            with open(self.hosts_file, 'r') as f:
                for line in f:
                    parts = line.strip().split()
                    if not parts:
                        continue
                    ship = parts[0]
                    # Extract company name from ship name (first part before hyphen)
                    if '-' in ship:
                        # Handle multi-part company names (e.g., anglo-american-shipping)
                        ship_parts = ship.split('-')
                        if len(ship_parts) >= 2:
                            # For names like "anglo-american-shipping-ubuntu-liberty",
                            # take first 2-3 parts as company name
                            if len(ship_parts) >= 3 and ship_parts[2] in ['shipping', 'marine', 'lines', 'group']:
                                company = '-'.join(ship_parts[:3]).title()
                            elif len(ship_parts) >= 2 and ship_parts[1] in ['american', 'eastern', 'western', 'northern', 'southern']:
                                company = '-'.join(ship_parts[:2]).title()
                            else:
                                company = ship_parts[0].title()
                        else:
                            company = ship_parts[0].title()
                    else:
                        company = "Other"
                    companies.setdefault(company, []).append((ship, line.strip()))

            # Create company sections with checkboxes
            for company, ship_lines in sorted(companies.items()):
                company_frame = self.create_company_section(company, ship_lines)
                self.scroll_layout.addWidget(company_frame)
                self.company_widgets.append(company_frame)

            # Add stretch to push everything to top
            self.scroll_layout.addStretch()

            # Update total vessel count
            self.update_total_vessels_count()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load ships: {e}")

    def update_total_vessels_count(self):
        """Update the total vessels count display"""
        total_count = len(self.ship_checkboxes)
        self.total_vessels_label.setText(f"Total: {total_count} vessels")

    def create_company_section(self, company, ship_lines):
        # Fluid company frame
        company_frame = QFrame()
        company_frame.setFrameStyle(QFrame.Shape.NoFrame)
        company_frame.setStyleSheet("""
            QFrame {
                background-color: #333333;
                border: none;
                border-radius: 8px;
                margin: 2px 0px;
                padding: 8px;
            }
            QFrame:hover {
                background-color: #383838;
            }
        """)

        layout = QVBoxLayout(company_frame)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(4)

        # Compact company header
        header_layout = QHBoxLayout()
        header_layout.setSpacing(8)

        # Company checkbox
        company_checkbox = CustomCheckBox()
        company_checkbox.stateChanged(lambda state, c=company: self.on_company_checkbox_changed(c, state))
        header_layout.addWidget(company_checkbox)

        # Enhanced company label - larger and more visible
        company_label = QLabel(f"{company} ({len(ship_lines)} ships)")
        company_label.setFont(QFont("Arial", 13, QFont.Weight.Bold))
        company_label.setStyleSheet("""
            background-color: transparent;
            color: #ffffff;
            padding: 4px 2px;
            min-height: 24px;
        """)
        header_layout.addWidget(company_label)

        header_layout.addStretch()

        # Fixed expand/collapse button
        expand_btn = QPushButton("▼")
        expand_btn.setMinimumWidth(35)
        expand_btn.setMaximumWidth(40)
        expand_btn.setMinimumHeight(32)
        expand_btn.setMaximumHeight(35)
        expand_btn.clicked.connect(lambda: self.toggle_company_section(company, expand_btn))
        header_layout.addWidget(expand_btn)

        layout.addLayout(header_layout)

        # Compact ships container (initially hidden)
        ships_widget = QWidget()
        ships_layout = QVBoxLayout(ships_widget)
        ships_layout.setContentsMargins(15, 3, 5, 3)
        ships_layout.setSpacing(2)

        # Store references
        company_frame.company_checkbox = company_checkbox
        company_frame.ships_widget = ships_widget
        company_frame.expand_btn = expand_btn
        company_frame.company_name = company
        company_frame.expanded = False

        # Create enhanced ship checkboxes
        for ship_name, raw_line in ship_lines:
            # Create a fluid styled widget for each ship row
            ship_widget = QWidget()
            ship_widget.setStyleSheet("""
                QWidget {
                    background-color: transparent;
                    border-radius: 6px;
                    margin: 1px 0px;
                    padding: 3px;
                }
                QWidget:hover {
                    background-color: #2e2e2e;
                }
            """)
            ship_layout = QHBoxLayout(ship_widget)
            ship_layout.setContentsMargins(8, 5, 8, 5)
            ship_layout.setSpacing(10)

            checkbox = CustomCheckBox()
            checkbox.stateChanged(lambda state, name=ship_name, c=company: self.on_ship_checkbox_changed(name, c, state))

            # Enhanced ship label - larger and more visible
            label = QLabel(ship_name)
            label.setFont(QFont("Arial", 12))
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background-color: transparent;
                    padding: 4px 6px;
                    min-height: 20px;
                }
            """)

            ship_layout.addWidget(checkbox)
            ship_layout.addWidget(label)
            ship_layout.addStretch()

            self.ship_checkboxes[ship_name] = {
                'checkbox': checkbox,
                'widget': ship_widget,
                'raw_line': raw_line,
                'company': company
            }

            ships_layout.addWidget(ship_widget)

        ships_widget.hide()
        layout.addWidget(ships_widget)

        return company_frame

    def toggle_company_section(self, company, expand_btn):
        # Find the company frame
        for company_frame in self.company_widgets:
            if hasattr(company_frame, 'company_name') and company_frame.company_name == company:
                if company_frame.expanded:
                    company_frame.ships_widget.hide()
                    expand_btn.setText("▼")
                    company_frame.expanded = False
                else:
                    company_frame.ships_widget.show()
                    expand_btn.setText("▲")
                    company_frame.expanded = True
                break

    def on_company_checkbox_changed(self, company, state):
        # Prevent recursive updates
        if self._updating_checkboxes:
            return

        try:
            self._updating_checkboxes = True
            checked = state == Qt.CheckState.Checked.value

            # Update all ship checkboxes in this company
            for ship_name, ship_data in self.ship_checkboxes.items():
                if ship_data['company'] == company:
                    ship_data['checkbox'].setChecked(checked)

                    # Update checked ships set
                    if checked:
                        self.checked_ships.add(ship_name)
                    else:
                        self.checked_ships.discard(ship_name)

            # Update selected vessels display
            self.update_selected_vessels_display()

        finally:
            self._updating_checkboxes = False

    def on_ship_checkbox_changed(self, ship_name, company, state):
        # Prevent recursive updates
        if self._updating_checkboxes:
            return

        try:
            self._updating_checkboxes = True
            checked = state == Qt.CheckState.Checked.value

            # Update checked ships set
            if checked:
                self.checked_ships.add(ship_name)
            else:
                self.checked_ships.discard(ship_name)

            # Update company checkbox state
            company_ships = [name for name, data in self.ship_checkboxes.items() if data['company'] == company]
            checked_ships = [name for name in company_ships if name in self.checked_ships]

            # Find company checkbox
            for company_frame in self.company_widgets:
                if hasattr(company_frame, 'company_name') and company_frame.company_name == company:
                    if len(checked_ships) == len(company_ships):
                        company_frame.company_checkbox.setChecked(True)
                    elif len(checked_ships) == 0:
                        company_frame.company_checkbox.setChecked(False)
                    else:
                        # Partial selection - for now just uncheck company checkbox
                        company_frame.company_checkbox.setChecked(False)
                    break

            # Update selected vessels display
            self.update_selected_vessels_display()

        finally:
            self._updating_checkboxes = False

    def filter_ships(self):
        query = self.search_input.text().lower().strip()

        # If no query, show everything collapsed
        if not query:
            self.reset_all_visibility()
            return

        normalized_query = query.replace('-', '').replace(' ', '')

        # Find all matching ships (both exact and partial matches)
        matching_ships = self.find_matching_ships(normalized_query)

        if not matching_ships:
            # No matches found - hide everything
            self.hide_all_companies()
            return

        # Apply the search results
        self.apply_search_results(matching_ships)

    def reset_all_visibility(self):
        """Reset all companies to collapsed state with all ships visible"""
        for company_frame in self.company_widgets:
            company_frame.show()
            company_frame.ships_widget.hide()
            company_frame.expand_btn.setText("▼")
            company_frame.expanded = False

            # Show all ships in this company
            company = company_frame.company_name
            for _, ship_data in self.ship_checkboxes.items():
                if ship_data['company'] == company:
                    ship_data['widget'].show()

    def find_matching_ships(self, normalized_query):
        """Find all ships that match the query and categorize them"""
        exact_matches = []
        partial_matches = []
        company_matches = []

        # Check for ship name matches
        for ship_name, _ in self.ship_checkboxes.items():
            normalized_ship_name = ship_name.lower().replace('-', '')

            # Check for exact ship name match
            if normalized_query == normalized_ship_name:
                exact_matches.append(ship_name)
                continue

            # Check if query matches any part of the ship name
            ship_parts = ship_name.lower().split('-')
            exact_part_match = False
            for part in ship_parts:
                if normalized_query == part.replace('-', ''):
                    exact_matches.append(ship_name)
                    exact_part_match = True
                    break

            if exact_part_match:
                continue

            # Check for partial matches
            if normalized_query in normalized_ship_name:
                partial_matches.append(ship_name)

        # Check for company name matches (only if no exact ship matches)
        if not exact_matches:
            for company_frame in self.company_widgets:
                normalized_company = company_frame.company_name.lower().replace('-', '').replace(' ', '')
                if normalized_query == normalized_company or normalized_query in normalized_company:
                    # Get all ships in this company
                    company_ships = [name for name, data in self.ship_checkboxes.items()
                                   if data['company'] == company_frame.company_name]
                    company_matches.extend(company_ships)

        return {
            'exact': exact_matches,
            'partial': partial_matches,
            'company': company_matches
        }

    def apply_search_results(self, matching_ships):
        """Apply search results with proper parent-child visibility"""
        # Priority: exact matches > company matches > partial matches
        if matching_ships['exact']:
            self.show_exact_matches(matching_ships['exact'])
        elif matching_ships['company']:
            self.show_company_matches(matching_ships['company'])
        elif matching_ships['partial']:
            self.show_partial_matches(matching_ships['partial'])

    def show_exact_matches(self, exact_matches):
        """Show only exact matching ships, auto-expanded in their companies"""
        companies_with_matches = set()

        # First, hide all companies and ships
        self.hide_all_companies()

        # Show only the exact matching ships and their companies
        for ship_name in exact_matches:
            ship_data = self.ship_checkboxes[ship_name]
            company = ship_data['company']
            companies_with_matches.add(company)

            # Show the ship
            ship_data['widget'].show()

        # Show and expand companies that have matching ships
        for company_frame in self.company_widgets:
            if company_frame.company_name in companies_with_matches:
                company_frame.show()
                # Auto-expand to show the matching ship
                company_frame.ships_widget.show()
                company_frame.expand_btn.setText("▲")
                company_frame.expanded = True

                # Hide all other ships in this company
                for ship_name, ship_data in self.ship_checkboxes.items():
                    if ship_data['company'] == company_frame.company_name:
                        if ship_name not in exact_matches:
                            ship_data['widget'].hide()

    def show_company_matches(self, company_ships):
        """Show companies that match, keep them collapsed"""
        companies_with_matches = set()

        # Hide all companies first
        self.hide_all_companies()

        # Determine which companies have matches
        for ship_name in company_ships:
            ship_data = self.ship_checkboxes[ship_name]
            companies_with_matches.add(ship_data['company'])

        # Show matching companies (collapsed) with all their ships visible
        for company_frame in self.company_widgets:
            if company_frame.company_name in companies_with_matches:
                company_frame.show()
                company_frame.ships_widget.hide()  # Keep collapsed
                company_frame.expand_btn.setText("▼")
                company_frame.expanded = False

                # Show all ships in this company
                for ship_name, ship_data in self.ship_checkboxes.items():
                    if ship_data['company'] == company_frame.company_name:
                        ship_data['widget'].show()

    def show_partial_matches(self, partial_matches):
        """Show partial matching ships, auto-expanded in their companies"""
        companies_with_matches = set()

        # Hide all companies first
        self.hide_all_companies()

        # Show partial matching ships
        for ship_name in partial_matches:
            ship_data = self.ship_checkboxes[ship_name]
            company = ship_data['company']
            companies_with_matches.add(company)
            ship_data['widget'].show()

        # Show and expand companies that have matching ships
        for company_frame in self.company_widgets:
            if company_frame.company_name in companies_with_matches:
                company_frame.show()
                # Auto-expand to show matching ships
                company_frame.ships_widget.show()
                company_frame.expand_btn.setText("▲")
                company_frame.expanded = True

                # Hide non-matching ships in this company
                for ship_name, ship_data in self.ship_checkboxes.items():
                    if ship_data['company'] == company_frame.company_name:
                        if ship_name not in partial_matches:
                            ship_data['widget'].hide()

    def hide_all_companies(self):
        """Hide all companies and their ships"""
        for company_frame in self.company_widgets:
            company_frame.hide()

        for _, ship_data in self.ship_checkboxes.items():
            ship_data['widget'].hide()

    def deploy(self):
        if not self.checked_ships:
            QMessageBox.warning(self, "No ships selected", "Please select at least one ship.")
            return

        version = self.version_combo.currentText()
        version_file = "/etc/ansible/playbooks/abomination/shipmate-desktop/selected_version"

        # Create new log tab for this deployment
        action_name = "Deploy"
        log_output = self.create_log_tab(action_name)

        try:
            with open(version_file, 'w') as vf:
                 vf.write(version + "\n")
        except Exception as e:
               log_output.append(f"⚠️ Failed to write selected version: {e}")
               return

        timestamp = self.get_timestamp()
        log_file = os.path.join(self.log_dir, f"deploy-{timestamp}.log")
        script_path = '/etc/ansible/playbooks/abomination/shipmate-desktop/script/run_deploy.sh'

        try:
            with open(log_file, 'w') as log:
                process = subprocess.Popen([script_path], stdout=log, stderr=log)

            # Store process and script info for killing
            if action_name in self.active_log_tabs:
                self.active_log_tabs[action_name]['process'] = process
                self.active_log_tabs[action_name]['script_name'] = 'run_deploy.sh'

            log_output.clear()
            log_output.append(f"Deployment started...")

        except Exception as e:
            log_output.append(f"Error starting deployment: {e}")

        self.tail_log_file_to_tab(log_file, action_name)

    def run_action(self, script_name, action_label, require_selection=True):
        if require_selection and not self.checked_ships:
            QMessageBox.warning(self, "No ships selected", f"Please select at least one ship for {action_label}.")
            return

        # Create new log tab for this action
        log_output = self.create_log_tab(action_label)

        # Write selected_hosts file if needed
        if require_selection:
            try:
                with open("/etc/ansible/playbooks/abomination/shipmate-desktop/selected_hosts", 'w') as f:
                    for ship_name in self.checked_ships:
                        if ship_name in self.ship_checkboxes:
                            raw_line = self.ship_checkboxes[ship_name]['raw_line']
                            f.write(raw_line + "\n")
            except Exception as e:
                log_output.append(f"⚠️ Failed to write selected_hosts: {e}")
                return

        # Continue with subprocess execution and logging
        timestamp = self.get_timestamp()
        log_file = os.path.join(self.log_dir, f"{action_label.replace(' ', '_').lower()}-{timestamp}.log")
        script_path = f"/etc/ansible/playbooks/abomination/shipmate-desktop/script/{script_name}"

        try:
            with open(log_file, 'w') as log:
                process = subprocess.Popen([script_path], stdout=log, stderr=log)

            # Store process and script info for killing
            if action_label in self.active_log_tabs:
                self.active_log_tabs[action_label]['process'] = process
                self.active_log_tabs[action_label]['script_name'] = script_name

            log_output.clear()
            log_output.append(f"{action_label} started...")

        except Exception as e:
            log_output.append(f"Error starting {action_label}: {e.__class__.__name__}: {e}")

        self.tail_log_file_to_tab(log_file, action_label)

    def get_timestamp(self):
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d-%H%M%S")
    
    def tail_log_file_to_tab(self, path, action_name):
        """Tail log file to a specific tab"""
        if action_name in self.active_log_tabs:
            log_output = self.active_log_tabs[action_name]['log_output']

            # Create a new log reader worker for this specific tab
            log_thread = LogReaderWorker(path)
            log_thread.new_line.connect(log_output.append)
            log_thread.start()

            # Store the thread reference in the tab info for cleanup
            self.active_log_tabs[action_name]['log_thread'] = log_thread


if __name__ == '__main__':
    app = QApplication(sys.argv)

    # Using custom checkbox widgets with dark theme
    print("Using custom checkbox widgets with dark theme styling")

    window = MainApp()
    window.show()
    sys.exit(app.exec())
