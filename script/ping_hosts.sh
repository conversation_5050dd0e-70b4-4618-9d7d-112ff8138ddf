#!/bin/bash

# Path to the hosts file
HOSTS_FILE="/etc/ansible/playbooks/abomination/shipmate-desktop/selected_hosts"

# Initialize counters for available and unavailable hosts
available_count=0
unavailable_count=0

# Loop through each line in the hosts file
while IFS= read -r line; do
    # Extract the ship name and IP address
    SHIP=$(echo "$line" | awk '{print $1}')
    HOST=$(echo "$line" | awk '{print $2}' | cut -d'=' -f2)

    # Ping the host
    if ping -c 2 -W 4 "$HOST" &> /dev/null; then
        echo "V - $SHIP"
        ((available_count++))
    else
        echo "X - $SHIP"
        ((unavailable_count++))
    fi
done < "$HOSTS_FILE"

# Calculate the total hosts
total_hosts=$((available_count + unavailable_count))

# Calculate the percentage of available hosts
if [[ $total_hosts -gt 0 ]]; then
    available_percentage=$(awk "BEGIN {printf \"%.2f\", ($available_count / $total_hosts) * 100}")
else
    available_percentage=0
fi

# Print statistics
echo ""
echo "Summary:"
echo "Available hosts: $available_count"
echo "Unavailable hosts: $unavailable_count"
echo "Percentage of available hosts: $available_percentage%"
