

---
- name: Restart dockers
  hosts: all
  become: true
  strategy: free
  gather_facts: no   # Disable fact gathering
  vars:
    ansible_ssh_common_args: '-o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'
  tasks:

    - name: Copy script from local machine to remote hosts
      copy:
        src: /etc/ansible/playbooks/shipmate/script/restart.sh
        dest: /tmp/restart.sh
      ignore_errors: true


    - name: Set execute permission on the script
      file:
        path: /tmp/restart.sh
        mode: '0755'
      ignore_errors: true


    - name: Run restart script
      shell: bash /tmp/restart.sh
      ignore_errors: true


    - name: Fetch the screenshot file to the local machine
      fetch:
        src: ~/Pictures/screenshot.jpg
        dest: ~/Documents/Screenshot/screenshot_{{ inventory_hostname }}.jpg
        flat: yes
      ignore_errors: true

