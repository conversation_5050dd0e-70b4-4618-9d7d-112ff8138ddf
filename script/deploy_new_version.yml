

---
- name: Upgrade new version
  hosts: all
  become: true
  strategy: free
  vars:
    ansible_ssh_common_args: '-o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'
  tasks:

    - name: Copy script from local machine to remote hosts
      copy:
        src: /etc/ansible/playbooks/shipmate/script/new_version.sh
        dest: /tmp/new_version.sh
      ignore_errors: true


    - name: Set execute permission on the script
      file:
        path: /tmp/new_version.sh
        mode: '0755'
      ignore_errors: true


    - name: Run the new version script with parameters
      shell: bash /tmp/new_version.sh {{ upgrade_version }}
      ignore_errors: true


    - name: Fetch files to the local machine
      fetch:
        src: "{{ item.src }}"
        dest: "~/Documents/Deploy_version_log/{{ item.dest_prefix }}_{{ inventory_hostname }}_{{ ansible_user }}{{ item.dest_suffix }}"
        flat: yes
      loop:
        - { src: "~/Pictures/screenshot.jpg", dest_prefix: "Screenshot", dest_suffix: ".jpg" }
        - { src: "/var/log/Deployed_new_version.txt", dest_prefix: "Deployed_new_version", dest_suffix: "_{{ ansible_date_time.date }}.txt" }
      ignore_errors: true

