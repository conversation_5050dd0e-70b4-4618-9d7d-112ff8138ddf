#!/bin/bash

# Remote server details
remote_user="orca-ghost"
remote_host="**********"
remote_port="39"
remote_file="/etc/ansible/hosts"
password="Orca123"

# Local output file
output_file="hosts"

# Empty the output file before writing
> "$output_file"

echo "Pulling new hosts from Ansible hosts file "
echo "......................................... "

# Extract the [ships] section from the remote hosts file, sort it, and save to output_file
sshpass -p "$password" ssh -o StrictHostKeyChecking=no -p "$remote_port" "$remote_user@$remote_host" "awk '/^\[ships\]/,/^$/' $remote_file | tail -n +2 | sort" > "$output_file"

echo "All new ship have been pulled from Ansible hosts file "

