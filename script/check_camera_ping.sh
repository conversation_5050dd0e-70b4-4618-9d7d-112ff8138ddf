#!/bin/bash

# List of IP addresses to ping
IP_ADDRESSES=(
    ************
    ************
    ************
    ************
    ************
    ************
    ************
    ************
)

# Read each line from the selected_hosts file
while IFS= read -r line; do
    # Extract connection details
    HOST=$(echo "$line" | awk '{print $2}' | cut -d'=' -f2)
    PORT=$(echo "$line" | awk '{print $3}' | cut -d'=' -f2)
    USER=$(echo "$line" | awk '{print $4}' | cut -d'=' -f2)
    PASSWORD=$(echo "$line" | awk '{print $5}' | cut -d'=' -f2)
    SHIP=$(echo "$line" | awk '{print $1}' )

    echo "Checking ping from $SHIP..."

    # Prepare the ping commands for all IPs
    PING_COMMANDS=""
    for IP in "${IP_ADDRESSES[@]}"; do
        PING_COMMANDS+="ping -c 3 $IP > /dev/null 2>&1 && echo 'Ping to $IP: SUCCESS' || echo 'Ping to $IP: FAILED'; "
    done

    # Execute all ping commands in one SSH session
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -p "$PORT" "$USER@$HOST" "$PING_COMMANDS"

done < /etc/ansible/playbooks/abomination/shipmate-desktop/selected_hosts

echo "Ping check process completed."
