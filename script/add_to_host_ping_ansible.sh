
#!/bin/bash

# Variables
REMOTE_USER="orca-ghost"
REMOTE_IP="**********"
REMOTE_PORT="39"
REMOTE_PASSWORD="Orca123"
REMOTE_FILE_PATH="/etc/ansible/hosts_ping"  
LOCAL_FILE="selected_hosts"


# Read the content of the local file
while IFS= read -r line
do
    # Append the line to the remote file using sshpass and SSH
    sshpass -p "$REMOTE_PASSWORD" ssh -p "$REMOTE_PORT" "$REMOTE_USER@$REMOTE_IP" "echo '$line' | tee -a $REMOTE_FILE_PATH"
done < "$LOCAL_FILE"

echo "Lines from $LOCAL_FILE have been appended to the remote file at $REMOTE_FILE_PATH"

