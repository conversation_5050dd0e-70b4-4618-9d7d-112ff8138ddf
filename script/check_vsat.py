#!/usr/bin/env python3
import paramiko
import sys
import time
import threading

sys.stdout.reconfigure(line_buffering=True)


def parse_hosts(file_path):
    """Parse the selected_hosts file and extract values from each line."""
    hosts = []
    try:
        with open(file_path, 'r') as file:
            for line in file:
                line = line.strip()
                if line:
                    # Extract the vessel name (first word in the line)
                    vessel_name = line.split()[0]

                    # Parse the key-value pairs from the line
                    key_values = line.split()
                    host_info = {"vessel_name": vessel_name}  # Store vessel name as part of the host_info dictionary
                    for pair in key_values[1:]:
                        key, value = pair.split("=")
                        host_info[key] = value
                    hosts.append(host_info)
        if not hosts:
            print("Error: No valid hosts found in the selected_hosts file.")
        return hosts
    except Exception as e:
        print(f"Error reading selected_hosts file: {e}")
        return []


def wait_for_prompt(channel, prompt, timeout=15):
    """Wait for a specific prompt in the channel output."""
    output = ""
    start_time = time.time()  # Using time.time() to track timeout
    while time.time() - start_time < timeout:
        if channel.recv_ready():
            output += channel.recv(65535).decode()
            if prompt.lower() in output.lower():
                return output
    return output


def check_interface(channel, interface="eth0.2"):
    """Check if the interface is up by running ifconfig command."""
    check_command = f"ifconfig | grep {interface} | wc -l\n"
    print(f"Running check: {check_command.strip()}")
    channel.send(check_command)
    time.sleep(1)  # Give time for the command to execute

    # Capture the result of the check command properly
    output = wait_for_prompt(channel, "#")  # Assuming "#" is the shell prompt
    output_lines = output.splitlines()

    # Debugging print to check actual command output
    print(f"Interface check output:\n{output}")

    for line in output_lines:
        stripped_line = line.strip()
        if stripped_line.isdigit() and int(stripped_line) >= 1:
            print(f"Interface {interface} is UP.")
            return True

    print(f"Interface {interface} is DOWN.")
    return False


def ssh_to_router_via_pu(pu_host, pu_port, pu_user, pu_password, router_host, router_port, router_user, router_password,
                         target_ip, interface, vessel_name):
    try:
        print(f"Connecting to PU at {pu_host}:{pu_port} as {pu_user}...")

        # Connect to PU
        pu_client = paramiko.SSHClient()
        pu_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        pu_client.connect(pu_host, port=pu_port, username=pu_user, password=pu_password)
        print(f"Successfully connected to PU.")

        print(f"Opening session to SSH into router {router_host}:{router_port} from PU...")
        channel = pu_client.get_transport().open_session()
        channel.get_pty()
        channel.invoke_shell()

        # Send SSH command to router
        router_ssh_command = f"ssh -o StrictHostKeyChecking=no -p {router_port} {router_user}@{router_host}\n"
        print(f"Sending command: {router_ssh_command.strip()}")
        channel.send(router_ssh_command)

        # Wait for password prompt from router
        print("Waiting for router password prompt...")
        output = wait_for_prompt(channel, "password")
        if "password" in output.lower():
            print("Password prompt detected. Sending password...")
            channel.send(f"{router_password}\n")
        else:
            print("Error: No password prompt detected for router.")
            print("Output received:", output)
            return

        # Check if the login was successful
        output = wait_for_prompt(channel, "Permission denied")
        if "Permission denied" in output:
            print("Router authentication failed. Check router credentials.")
            return
        print("Successfully authenticated to the router.")

        # Check the interface status
        interface_up = check_interface(channel, interface)

        # Execute ping command based on interface status
        if interface_up:
            ping_command = f"ping -I {interface} -c 10 {target_ip}\n"
            print(f"Executing ping command for vessel {vessel_name}: {ping_command.strip()}")
            channel.send(ping_command)
        else:
            ping_command_2 = f"ping -I {interface2} -c 5 {target_ip}\n"
            print(f"Executing ping command 2 for vessel {vessel_name}: {ping_command_2.strip()}")
            channel.send(ping_command_2)

            time.sleep(2)

        # Capture and display at least 5 lines of ping results
        print("Waiting for ping results...")
        output = ""
        lines_received = 0  # Counter for the lines received
        while lines_received < 7:  # We want at least 7 lines of output
            if channel.recv_ready():
                chunk = channel.recv(65535).decode()
                output += chunk
                lines_received += chunk.count("\n")  # Increment by the number of lines in this chunk
            if channel.recv_stderr_ready():
                error_output = channel.recv_stderr(65535).decode()
                print("Error Output:", error_output)

        print(f"Ping Results for vessel {vessel_name}:")
        print(output)

        print("Closing connections...")
        channel.close()
        pu_client.close()
        print("Connections closed.")
        print("---------------------------------------------------------")

    except Exception as e:
        print(f"Error occurred: {e}")
        print("---------------------------------------------------------")


# Main execution
if __name__ == "__main__":
    selected_hosts_file = "/etc/ansible/playbooks/abomination/shipmate-desktop/selected_hosts"  # Path to the file

    hosts = parse_hosts(selected_hosts_file)
    if hosts:
        # Static values for router and target IP (modify as needed)
        router_host = "*************"  # Router IP
        router_user = "root"  # Router username
        router_password = "Orca123456"  # Router password
        target_ip = "*******"  # Target IP to ping
        router_port = 22  # Router SSH port
        interface = "eth0.2"  # Interface to use for ping
        interface2 = "eth1"

        # Iterate over each host and run the function
        for host_info in hosts:
            vessel_name = host_info.get("vessel_name")  # Extract vessel name (n-number)
            print(f"Vessel Name: {vessel_name}")

            pu_host = host_info.get("ansible_host")
            pu_user = host_info.get("ansible_user")
            pu_port = int(host_info.get("ansible_port", 22))
            pu_password = host_info.get("ansible_password")
            if pu_password:
             pu_password = pu_password.strip('"').strip("'")

            # Ensure this function call is indented properly (no extra spaces or tabs)
            ssh_to_router_via_pu(pu_host, pu_port, pu_user, pu_password, router_host, router_port, router_user,
                                 router_password, target_ip, interface, vessel_name)
    else:
        print("Failed to retrieve host information from the selected_hosts file.")