
#!/bin/bash


# Set the log directory and log file
LOG_DIR="/orcaai/orcaai-storage/"
LOG_FILE="$LOG_DIR/Sanity_log.txt"

add_spaces() {
    echo -e "\n\n\n" >> "$LOG_FILE"
}

# Create the log directory if it doesn't exist
mkdir -p $LOG_DIR
# Clean the file
echo -e "\n" > $LOG_FILE

echo $(hostname) >> $LOG_FILE
sudo docker ps | awk '$NF == "system-manager" {split($2, a, ":"); print a[2]}' >> $LOG_FILE
add_spaces


echo "Overall_Errors:" >> $LOG_FILE
timeout 30s sudo bash -c "tail /orcaai/orcaai-storage/upload/logs/fluentd/buffer.*.log /orcaai/orcaai-storage/upload/logs/host-fluentd/buffer.*.log -f | grep ERROR" >> $LOG_FILE 2>&1
add_spaces


echo "Restarting detector-service:" >> $LOG_FILE
sudo docker restart detector-service >> $LOG_FILE 2>&1
add_spaces


echo "AIS Service 20:" >> $LOG_FILE
curl "http://localhost:5008/targets?distance_stop=20" | grep target_id -o | wc -l >> $LOG_FILE 2>&1
add_spaces


echo "AIS Service 10:" >> $LOG_FILE
curl "http://localhost:5008/targets?distance_stop=10" | grep target_id -o | wc -l >> $LOG_FILE 2>&1
add_spaces


echo "Radar Service:" >> $LOG_FILE
curl -s "http://localhost:5006/health_check" >> $LOG_FILE 2>&1
add_spaces


echo "UI Health:" >> $LOG_FILE
curl "http://localhost:5012/clients/main/health"  >> $LOG_FILE 2>&1
add_spaces


echo "Stitch Service:" >> $LOG_FILE
curl "http://localhost:5005/health_check"    >> $LOG_FILE 2>&1
add_spaces

echo "MQTT:" >> $LOG_FILE
curl "http://localhost:5029/health_check"  >> $LOG_FILE 2>&1
add_spaces

echo "GPS & Gyro:" >> $LOG_FILE
curl "http://localhost:5010/pvt" | python3 -m json.tool >> $LOG_FILE 2>&1
add_spaces


echo "Exited Services:" >> $LOG_FILE
sudo docker ps -f health=unhealthy >> $LOG_FILE 2>&1
add_spaces


echo "Ping:" >> $LOG_FILE
# Define the list of IPs to ping
IP_LIST=(
    ************
    ************
    ************
    ************
    ************
    ************
    ************
    ************
)

# Loop through the IPs and perform the ping
for IP in "${IP_LIST[@]}"; do
    if ping -c 3 "$IP" &>/dev/null; then
        echo "$IP ping successfully" >> $LOG_FILE
    else
        echo "$IP failed ping !!!!" >> $LOG_FILE
    fi
done
add_spaces


echo "IMU Service:" >> $LOG_FILE

# Run the curl command and capture the output
RESPONSE=$(curl -s "http://localhost:5007/data")

# Check for the required words in the response
if [[ $RESPONSE == *"pitch"* && $RESPONSE == *"roll"* && $RESPONSE == *"packet_timestamp"* ]]; then
    echo "IMU is working successfully" >> $LOG_FILE
else
    echo "IMU failed!!!!" >> $LOG_FILE

    # Run the commands to attempt recovery
    echo "Attempting to recover IMU..." >> $LOG_FILE
    sudo rm -r /dev/ttyIMU
    sudo udevadm control --reload-rules
    sudo udevadm trigger
    sudo usbreset 10c4:ea60
    sudo docker restart imu-service
    sleep 35


    # Log the completion of recovery steps
    echo "IMU recovery steps executed. Rechecking the IMU status again..." >> $LOG_FILE

    # Run the curl command again to check the updated status
    RESPONSE=$(curl -s "http://localhost:5007/data")
    if [[ $RESPONSE == *"pitch"* && $RESPONSE == *"roll"* && $RESPONSE == *"packet_timestamp"* ]]; then
        echo "IMU is working successfully after recovery." >> $LOG_FILE
    else
        echo "IMU failed again!!!!" >> $LOG_FILE
    fi
fi

add_spaces

echo "Restarting Services:" >> $LOG_FILE
sudo docker ps -f status=restarting | tail -n +2 >> $LOG_FILE 2>&1
add_spaces


echo "Unhealthy Services:" >> $LOG_FILE
sudo docker ps -f status=exited | grep -vvv 'make\|ship-cli' >> $LOG_FILE 2>&1
add_spaces

echo "Detector-service:" >> $LOG_FILE
sudo docker exec influxdb influx -database 'orca_ai' -execute 'select full_detection_cycle_time from detector_full_cycle_time where time > now() - 5m limit 10' -format 'json' -pretty >> $LOG_FILE 2>&1
add_spaces

echo "Scene-manager - Start recording:" >> $LOG_FILE
curl --request POST "http://localhost:3000/recordings" --data '{"duration": 120, "trigger": "recording_test", "videoContainer": "mp4", "fps": 15, "quality": "high", "recordScreen": true, "recordSensors": true, "cameraIds": ["day_center", "day_left", "day_right","thermal_center","thermal_right","thermal_left","day_left_2nd","day_right_2nd"]}'  | grep '^{"message"' >> $LOG_FILE 2>&1
add_spaces


echo "Scene-manager - Check scene_name:" >> $LOG_FILE
curl "http://localhost:3000/health_check" -sb -H  >> $LOG_FILE 2>&1
add_spaces


echo "Local influx data:" >> $LOG_FILE
timeout -sHUP 5s bash -c -- "while true; do sudo docker ps | grep influxdb; sleep 1; done" >> $LOG_FILE 2>&1
add_spaces


echo "Interface:" >> $LOG_FILE
echo $(curl "http://localhost:8500/v1/kv/shared/sensor_net_iface_addr" | python3 -m json.tool | grep Value | awk '{print $2}' | tr -d '",') | base64 -d >> $LOG_FILE 2>&1
ip a | grep 192.168.1 >> $LOG_FILE 2>&1
add_spaces


echo "System State:" >> $LOG_FILE
curl "http://localhost:5012/systemState" | python3 -m json.tool >> $LOG_FILE 2>&1
add_spaces



echo "Network Speed:" >> $LOG_FILE
for iface in enp3s0 enp4s0 eno2; do sudo ethtool $iface | grep Speed >> $LOG_FILE; done
add_spaces


echo "Resolution:" >> $LOG_FILE
export DISPLAY=:0
xrandr | head -n 25 >> $LOG_FILE 2>&1
add_spaces


echo "ffprobe:" >> $LOG_FILE
for i in {0..7}; do ffprobe /dev/video$i 2>> $LOG_FILE; done
add_spaces


echo "Communication-engine:" >> $LOG_FILE
curl "http://localhost:5019/health_check" | python3 -m json.tool >> $LOG_FILE 2>&1
add_spaces


echo "Directories Protection Permission changed succsesfully " >> $LOG_FILE
sudo chmod 600 -R /orcaai/orcaai-storage/config/ /orcaai/orcaai-storage/upload/ >> $LOG_FILE 2>&1
add_spaces


echo "Mask Validation:" >> $LOG_FILE
timeout 30s sudo bash -c "tail /orcaai/orcaai-storage/upload/logs/fluentd/buffer.*.log /orcaai/orcaai-storage/upload/logs/host-fluentd/buffer.*.log -f | grep detector-service" >> $LOG_FILE 2>&1
add_spaces


echo "PU Temperature:" >> $LOG_FILE
sensors | head -n 23 >> $LOG_FILE 2>&1
add_spaces


echo "Nvidia SMI:" >> $LOG_FILE
nvidia-smi | head -n 12 >> $LOG_FILE 2>&1
add_spaces


echo "CPU Usage:" >> $LOG_FILE
mpstat -P ALL >> $LOG_FILE 2>&1
sudo free -h >> $LOG_FILE 2>&1
add_spaces



# Take a screenshot
DISPLAY=":0.0" import -window root ~/Pictures/screenshot.jpg

# Command to delete the script
rm -- "$0"

