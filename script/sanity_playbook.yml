

---
- name: Sanity
  hosts: all
  become: true
  strategy: free
  vars:
    ansible_ssh_common_args: '-o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'
  tasks:

    - name: Copy script from local machine to remote hosts
      copy:
        src: /etc/ansible/playbooks/shipmate/script/sanity.sh
        dest: /tmp/sanity.sh
      ignore_errors: true


    - name: Set execute permission on the script
      file:
        path: /tmp/sanity.sh
        mode: '0755'
      ignore_errors: true


    - name: Run the sanity script
      shell: bash /tmp/sanity.sh
      ignore_errors: true


    - name: Fetch files to the local machine
      fetch:
        src: "{{ item.src }}"
        dest: "~/Documents/Sanity/{{ item.dest_prefix }}_{{ inventory_hostname }}_{{ ansible_user }}{{ item.dest_suffix }}"
        flat: yes
      loop:
        - { src: "~/Pictures/screenshot.jpg", dest_prefix: "Screenshot", dest_suffix: ".jpg" }
        - { src: "/orcaai/orcaai-storage/Sanity_log.txt", dest_prefix: "Sanity", dest_suffix: "_{{ ansible_date_time.date }}.txt" }
      ignore_errors: true


