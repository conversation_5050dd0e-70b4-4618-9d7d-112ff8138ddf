#!/bin/bash

# Remote server details

REMOTE_USER="orca-ghost"
REMOTE_IP="**********"
REMOTE_PORT="39"
REMOTE_PASSWORD="Orca123"

REMOTE_FILE_PATH="/etc/ansible/hosts_ping"
LOCAL_FILE="selected_hosts"

# Check if local file exists
if [[ ! -f "$LOCAL_FILE" ]]; then
    echo "Local file $LOCAL_FILE does not exist."
    exit 1
fi

# Iterate over each line in the local file
while IFS= read -r line
do
    # Remove the line from the remote file
    sshpass -p "$REMOTE_PASSWORD" ssh -p "$REMOTE_PORT" "$REMOTE_USER@$REMOTE_IP" \
    "sudo sed -i '/$line/d' $REMOTE_FILE_PATH" || {
        echo "Failed to remove line: $line"
        exit 1
    }
done < "$LOCAL_FILE"

echo "Lines from $LOCAL_FILE have been removed from the remote file at $REMOTE_FILE_PATH"
