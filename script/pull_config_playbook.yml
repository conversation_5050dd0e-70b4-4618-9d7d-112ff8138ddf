

---
- name: Pull Config
  hosts: all
  become: true
  strategy: free
  gather_facts: no   # Disable fact gathering
  vars:
    ansible_ssh_common_args: '-o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'
  tasks:

    - name: Copy script from local machine to remote hosts
      copy:
        src: /etc/ansible/playbooks/shipmate/script/pull_config.sh
        dest: /tmp/pull_config.sh
      ignore_errors: true


    - name: Set execute permission on the script
      file:
        path: /tmp/pull_config.sh
        mode: '0755'
      ignore_errors: true


    - name: Run restart script
      shell: bash /tmp/pull_config.sh
      ignore_errors: true


    - name: Fetch the log file to the local machine
      fetch:
        src: /tmp/pull_config.txt
        dest: ~/Documents/Config/pull_config_logs_{{ inventory_hostname }}.txt
        flat: yes
      ignore_errors: true


    - name: Delete the log file and the script file from the remote machine
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /tmp/pull_config.sh
        - /tmp/pull_config.txt
      ignore_errors: true

