#!/bin/bash

# Parameters 
CURRENT_VERSION=$(sudo docker ps | awk '$NF == "system-manager" {split($2, a, ":"); print a[2]}')
NEW_VERSION=$1
SHIP=$(hostname)

# Define the filename for the logs
LOG_DIR="/var/log/"
FILENAME="${LOG_DIR}Deployed_new_version.txt"


# Function to take a screenshot and then delete the script
cleanup() {
    DISPLAY=":0.0" import -window root ~/Pictures/screenshot.jpg

    #Backup the logs file under /vat/log
    TIMESTAMP=$(date +"%Y-%m-%d_%H-%M-%S")
    BACKUP_FILENAME="${FILENAME%.txt}_$TIMESTAMP.txt"
    cp "$FILENAME" "$BACKUP_FILENAME"

    #Delete the script
    rm -- "$0"
}

# Set trap to run cleanup before exiting
trap 'cleanup' EXIT

echo "" > $FILENAME
echo $(hostname) >> $FILENAME
echo "" >> $FILENAME

if [ "$CURRENT_VERSION" == "$NEW_VERSION" ]; then
    echo "new version already installed on the ship" >> "$FILENAME"
    exit 0
fi



# Get the image ID and count of images with the new version

image_count=$(sudo docker images | grep $NEW_VERSION | wc -l)


if [[ "$image_count" -ge 27 ]]; then


        # Run the Docker command and capture its output
    output=$(    sudo docker run -it \
      -e CONFIG_PROVIDER_PROD_URL='************' \
      -e CUDA_VERSION=$(nvidia-smi | grep 'CUDA Version:' | cut -d ':' -f3 | sed -r 's/[|]+/ /g' | xargs) \
      -e DISPLAY=$(w -hs | grep -Po "[ \t]:[\.0-9A-Za-z:]+[ \t]" | sort -u | xargs) \
      -v ~/.docker/config.json:/root/.docker/config.json \
      -v /var/run/docker.sock:/var/run/docker.sock \
      -v /orcaai/orcaai-storage:/orcaai/orcaai-storage \
      -v /dev:/dev \
      -v /lib/modules:/lib/modules \
      -v /etc/rc.local:/host/rc.local \
      -v /run/systemd/system:/run/systemd/system \
      -v /var/run/dbus/system_bus_socket:/var/run/dbus/system_bus_socket \
      -v $XAUTH:/root/.Xauthority \
      -v /tmp/.X11-unix:/tmp/.X11-unix \
      --network host \
      --privileged \
      --cap-add=ALL \
      registry.gitlab.com/orca_ai/orcaai-ship/ship-cli:$NEW_VERSION \
      make set-cloud-configs ship=$SHIP version=$NEW_VERSION >> $FILENAME 2>&1)

    # Check if the command failed
    if [ $? -ne 0 ]; then
        echo "Failed to pull config. Exiting..." >> $FILENAME
        exit 1
    fi

    # Check if the output contains '"message": "done"'
    if grep -q '"message": "done"' "$FILENAME"; then
        echo "Success! Continuing..." >> $FILENAME
    else
        echo "Message not found. Exiting..." >> $FILENAME
        exit 1
    fi

    # Stop all services
    sudo docker run --rm -it \
      -e CUDA_VERSION=$(nvidia-smi | grep 'CUDA Version:' | cut -d ':' -f3 | sed -r 's/[|]+/ /g' | xargs) \
      -e DISPLAY=$(w -hs | grep -Po "[ \t]:[\.0-9A-Za-z:]+[ \t]" | sort -u | xargs) \
      -e AUDIO_GROUP=$(getent group audio | cut -d: -f3) \
      -e XDG_RUNTIME_DIR_VALUE=${XDG_RUNTIME_DIR} \
      -v ~/.docker/config.json:/root/.docker/config.json \
      -v /var/run/docker.sock:/var/run/docker.sock \
      -v /orcaai/orcaai-storage:/orcaai/orcaai-storage \
      -v /dev:/dev \
      -v /lib/modules:/lib/modules \
      -v /etc/rc.local:/host/rc.local \
      -v /run/systemd/system:/run/systemd/system \
      -v /var/run/dbus/system_bus_socket:/var/run/dbus/system_bus_socket \
      -v $XAUTH:/root/.Xauthority \
      -v /tmp/.X11-unix:/tmp/.X11-unix \
      -v ${XDG_RUNTIME_DIR}/pulse/native:${XDG_RUNTIME_DIR_VALUE}/pulse/native \
      --network host \
      --privileged \
      --cap-add=ALL \
      registry.gitlab.com/orca_ai/orcaai-ship/ship-cli:$CURRENT_VERSION \
      make stop-all

    # Log to file
    echo "Stopped all services at $(date)" >> $FILENAME
    sleep 40
    echo "$(sudo docker ps -q | wc -l) services are running after stopping the services" >> $FILENAME
    count_after_stop=$(sudo docker ps -q | wc -l)
    echo "" >> $FILENAME

    if [[ "$count_after_stop" -gt 5 ]]; then
    	echo "Services didn't stop, Please check why" >> $FILENAME
    	echo "" >> $FILENAME
    	sudo docker ps >> $FILENAME 2>&1
    	exit 1
    fi

    # Run infra services
    sudo docker run --rm -it \
      -e CUDA_VERSION=$(nvidia-smi | grep 'CUDA Version:' | cut -d ':' -f3 | sed -r 's/[|]+/ /g' | xargs) \
      -e DISPLAY=$(w -hs | grep -Po "[ \t]:[\.0-9A-Za-z:]+[ \t]" | sort -u | xargs) \
      -e AUDIO_GROUP=$(getent group audio | cut -d: -f3) \
      -e XDG_RUNTIME_DIR_VALUE=${XDG_RUNTIME_DIR} \
      -v ~/.docker/config.json:/root/.docker/config.json \
      -v /var/run/docker.sock:/var/run/docker.sock \
      -v /orcaai/orcaai-storage:/orcaai/orcaai-storage \
      -v /dev:/dev \
      -v /lib/modules:/lib/modules \
      -v /etc/rc.local:/host/rc.local \
      -v /run/systemd/system:/run/systemd/system \
      -v /var/run/dbus/system_bus_socket:/var/run/dbus/system_bus_socket \
      -v $XAUTH:/root/.Xauthority \
      -v /tmp/.X11-unix:/tmp/.X11-unix \
      -v ${XDG_RUNTIME_DIR}/pulse/native:${XDG_RUNTIME_DIR_VALUE}/pulse/native \
      --network host \
      --privileged \
      --cap-add=ALL \
      registry.gitlab.com/orca_ai/orcaai-ship/ship-cli:$NEW_VERSION \
      make run-infra

    # Log to file
    echo "Ran infra services at $(date)" >> $FILENAME
    sleep 10
    echo "$(sudo docker ps -q | wc -l) services are running after starting the Infra services" >> $FILENAME
    echo "" >> $FILENAME
    echo "" >> $FILENAME


    # Log to file
    echo "Set cloud config at $(date)" >> $FILENAME

    sleep 10

    # Run influx
    sudo docker run --rm -it \
      -e CUDA_VERSION=$(nvidia-smi | grep 'CUDA Version:' | cut -d ':' -f3 | sed -r 's/[|]+/ /g' | xargs) \
      -e DISPLAY=$(w -hs | grep -Po "[ \t]:[\.0-9A-Za-z:]+[ \t]" | sort -u | xargs) \
      -e AUDIO_GROUP=$(getent group audio | cut -d: -f3) \
      -e XDG_RUNTIME_DIR_VALUE=${XDG_RUNTIME_DIR} \
      -v ~/.docker/config.json:/root/.docker/config.json \
      -v /var/run/docker.sock:/var/run/docker.sock \
      -v /orcaai/orcaai-storage:/orcaai/orcaai-storage \
      -v /dev:/dev \
      -v /lib/modules:/lib/modules \
      -v /etc/rc.local:/host/rc.local \
      -v /run/systemd/system:/run/systemd/system \
      -v /var/run/dbus/system_bus_socket:/var/run/dbus/system_bus_socket \
      -v $XAUTH:/root/.Xauthority \
      -v /tmp/.X11-unix:/tmp/.X11-unix \
      -v ${XDG_RUNTIME_DIR}/pulse/native:${XDG_RUNTIME_DIR_VALUE}/pulse/native \
      --network host \
      --privileged \
      --cap-add=ALL \
      registry.gitlab.com/orca_ai/orcaai-ship/ship-cli:$NEW_VERSION \
      make influx-retention-policy

    # Log to file
    echo "Ran influx at $(date)" >> $FILENAME


    sleep 10

    # Run start new version
    sudo docker run --rm -it \
      -e CUDA_VERSION=$(nvidia-smi | grep 'CUDA Version:' | cut -d ':' -f3 | sed -r 's/[|]+/ /g' | xargs) \
      -e DISPLAY=$(w -hs | grep -Po "[ \t]:[\.0-9A-Za-z:]+[ \t]" | sort -u | xargs) \
      -e AUDIO_GROUP=$(getent group audio | cut -d: -f3) \
      -e XDG_RUNTIME_DIR_VALUE=${XDG_RUNTIME_DIR} \
      -v ~/.docker/config.json:/root/.docker/config.json \
      -v /var/run/docker.sock:/var/run/docker.sock \
      -v /orcaai/orcaai-storage:/orcaai/orcaai-storage \
      -v /dev:/dev \
      -v /lib/modules:/lib/modules \
      -v /etc/rc.local:/host/rc.local \
      -v /run/systemd/system:/run/systemd/system \
      -v /var/run/dbus/system_bus_socket:/var/run/dbus/system_bus_socket \
      -v $XAUTH:/root/.Xauthority \
      -v /tmp/.X11-unix:/tmp/.X11-unix \
      -v ${XDG_RUNTIME_DIR}/pulse/native:${XDG_RUNTIME_DIR_VALUE}/pulse/native \
      --network host \
      --privileged \
      --cap-add=ALL \
      registry.gitlab.com/orca_ai/orcaai-ship/ship-cli:$NEW_VERSION \
      make start-version VERSION=$NEW_VERSION

    # Log to file
    echo "Started new version at $(date)" >> $FILENAME
    echo "" >> $FILENAME
    echo "" >> $FILENAME
    echo "" >> $FILENAME

    sleep 40


    echo "IMU Service:" >> $FILENAME

    # Run the curl command and capture the output
    RESPONSE=$(curl -s "http://localhost:5007/data")

    # Check for the required words in the response
    if [[ $RESPONSE == *"pitch"* && $RESPONSE == *"roll"* && $RESPONSE == *"packet_timestamp"* ]]; then
        echo "IMU is working successfully" >> $FILENAME
    else
        echo "IMU failed!!!!" >> $FILENAME

        # Run the commands to attempt recovery
        echo "Attempting to recover IMU..." >> $FILENAME
        sudo rm -r /dev/ttyIMU
        sudo udevadm control --reload-rules
        sudo udevadm trigger
        sudo usbreset 10c4:ea60
        sudo docker restart imu-service
        sleep 9

        # Run the curl command again to check the updated status
        RESPONSE=$(curl -s "http://localhost:5007/data")
        if [[ $RESPONSE == *"pitch"* && $RESPONSE == *"roll"* && $RESPONSE == *"packet_timestamp"* ]]; then
            echo "IMU is working successfully after recovery." >> $FILENAME
        else
            echo "IMU failed again!!!!" >> $FILENAME
        fi
    fi


    # Log to file
    echo "" >> $FILENAME
    echo "$(sudo docker ps -q | wc -l) services are running" >> $FILENAME
    echo "" >> $FILENAME
    echo "" >> $FILENAME
    echo "" >> $FILENAME

    sudo docker ps --format '{{.Image}}       {{.Status}}' | sed 's#registry.gitlab.com/orca_ai/orcaai-ship/##' | sed 's#registry.gitlab.com/orca_ai/orcaai-docker/##' >> $FILENAME 2>&1


else
    echo " Check the docker images of $NEW_VERSION , you need to run the download script again" >> $FILENAME
    echo "" >> $FILENAME
    sudo docker ps >> $FILENAME 2>&1
    exit 1
fi





