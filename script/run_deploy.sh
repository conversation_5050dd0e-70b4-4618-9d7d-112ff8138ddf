#!/bin/bash

# Inventory file and playbook
INVENTORY_FILE="/etc/ansible/playbooks/abomination/shipmate-desktop/selected_hosts"

# Read the version to upgrade to from the file
VERSION_FILE="/etc/ansible/playbooks/abomination/shipmate-desktop/selected_version"

if [ -f "$VERSION_FILE" ]; then
    UPGRADE_VERSION=$(cat "$VERSION_FILE" | tr -d '\r\n')
else
    echo "Version file not found: $VERSION_FILE"
    exit 1
fi

# List of hosts in the [deploy] group

echo ""
echo "The following hosts will be affected:"
echo ""
awk '{print $1, $4}' "$INVENTORY_FILE"
echo
echo ""

# Prompt for confirmation
echo "The above ship will be upgraded to   $UPGRADE_VERSION  " 

ansible-playbook -i "$INVENTORY_FILE" script/deploy_new_version.yml --extra-vars "upgrade_version=$UPGRADE_VERSION" -f 50 -T 50


