
#!/bin/bash

# Set the log directory and log file
LOG_DIR="/orcaai/orcaai-storage/"
LOG_FILE="$LOG_DIR/Sanity_log.txt"

add_spaces() {
    echo -e "\n\n\n" >> "$LOG_FILE"
}

# Create the log directory if it doesn't exist
mkdir -p $LOG_DIR
# Clean the file
echo -e "\n" > $LOG_FILE

echo $(hostname) >> $LOG_FILE
sudo docker ps | awk '$NF == "system-manager" {split($2, a, ":"); print a[2]}' >> $LOG_FILE
add_spaces


echo "Check for AIS targets - Number below needs to be greater then 1:" >> $LOG_FILE
curl "http://localhost:5008/targets?distance_stop=100" | grep target_id -o | wc -l >> $LOG_FILE 2>&1
add_spaces



echo "Check for GPS(latitude/longitude/cog/sog) & Gyro (heading/rot): (Make sure no NULL!!!) " >> $LOG_FILE
curl "http://localhost:5010/pvt" | python3 -m json.tool >> $LOG_FILE 2>&1
add_spaces


echo "Check connection to all cameras: (For panorama to work)" >> $LOG_FILE
# Define the list of IPs to ping
IP_LIST=(
    ************
    ************
    ************
    ************
    ************
    ************
    ************
    ************
)

# Loop through the IPs and perform the ping
for IP in "${IP_LIST[@]}"; do
    if ping -c 3 "$IP" &>/dev/null; then
        echo "$IP Connection successfully" >> $LOG_FILE
    else
        echo "$IP failed ping !!!!" >> $LOG_FILE
    fi
done
add_spaces



if [ $(ls /orcaai/orcaai-storage/masks/day* 2>/dev/null | wc -l) -gt 2 ]; then
    echo "Mask was done" >> $LOG_FILE
else
    echo "Mask wasn't done" >> $LOG_FILE
fi

add_spaces

# Take a screenshot
DISPLAY=":0.0" import -window root ~/Pictures/screenshot.jpg
#ffmpeg -y -f video4linux2 -i /dev/video9 -frames:v 1 ~/Pictures/thermal_panorama.jpg
#ffmpeg -y -f video4linux2 -i /dev/video8 -frames:v 1 ~/Pictures/day_panorama.jpg

# Command to delete the script
rm -- "$0"

