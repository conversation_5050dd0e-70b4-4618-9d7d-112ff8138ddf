#!/bin/bash

# Directory to save screenshots
LOCAL_SAVE_DIR="$HOME/Documents/Screenshot"

# Ensure the local directory exists
mkdir -p "$LOCAL_SAVE_DIR"

# Read each line from the selected_hosts file
while IFS= read -r line; do
    # Extract connection details
    HOST=$(echo "$line" | awk '{print $2}' | cut -d'=' -f2)
    PORT=$(echo "$line" | awk '{print $3}' | cut -d'=' -f2)
    USER=$(echo "$line" | awk '{print $4}' | cut -d'=' -f2)
    PASSWORD=$(echo "$line" | awk '{print $5}' | cut -d'=' -f2)
    SHIP=$(echo "$line" | awk '{print $1}' )

    # Run the screenshot command on the remote server and log output
    echo "Running screenshot command on $SHIP..."
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -p "$PORT" "$USER@$HOST" "DISPLAY=':0.0' import -window root ~/Pictures/screenshot.jpg" < /dev/null 2>&1 | tee -a screenshot_error.log

    # Pull the screenshot back to the local machine if it was successful
    if sshpass -p "$PASSWORD" scp -P "$PORT" "$USER@$HOST:/home/<USER>/Pictures/screenshot.jpg" "$LOCAL_SAVE_DIR/screenshot_${SHIP}.jpg"; then
        echo "Screenshot successfully pulled from $SHIP."
    else
        echo "Failed to pull screenshot from $SHIP. Check screenshot_error.log for details."
    fi

done < /etc/ansible/playbooks/abomination/shipmate-desktop/selected_hosts

echo "Screenshots pulling process completed."

