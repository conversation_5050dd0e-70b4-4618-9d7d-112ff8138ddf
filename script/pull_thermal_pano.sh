#!/bin/bash

# Directory to save screenshots
LOCAL_SAVE_DIR="$HOME/Documents/Screenshot"

# Ensure the local directory exists
mkdir -p "$LOCAL_SAVE_DIR"

# Read each line from the selected_hosts file
while IFS= read -r line; do
    # Extract connection details
    HOST=$(echo "$line" | awk '{print $2}' | cut -d'=' -f2)
    PORT=$(echo "$line" | awk '{print $3}' | cut -d'=' -f2)
    USER=$(echo "$line" | awk '{print $4}' | cut -d'=' -f2)
    PASSWORD=$(echo "$line" | awk '{print $5}' | cut -d'=' -f2 | tr -d '"')
    SHIP=$(echo "$line" | awk '{print $1}' )

    # Run the screenshot command on the remote server and log output silently
    echo "Running screenshot command on $SHIP..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -p "$PORT" "$USER@$HOST" \
        "ffmpeg -y -f video4linux2 -i /dev/video9 -frames:v 1 ~/Pictures/thermal_panorama.jpg" \
        < /dev/null >> screenshot_error.log 2>&1; then
        echo "Screenshot command executed successfully on $SHIP."
    else
        echo "Screenshot command failed on $SHIP. Check screenshot_error.log for details."
        continue
    fi

    # Pull the screenshot back to the local machine if it was successful
    if sshpass -p "$PASSWORD" scp -P "$PORT" "$USER@$HOST:/home/<USER>/Pictures/thermal_panorama.jpg" \
        "$LOCAL_SAVE_DIR/thermal_panorama_${SHIP}.jpg" >> screenshot_error.log 2>&1; then
        echo "Screenshot successfully pulled from $SHIP."
    else
        echo "Failed to pull screenshot from $SHIP. Check screenshot_error.log for details."
    fi

done < /etc/ansible/playbooks/abomination/shipmate-desktop/selected_hosts

echo "Screenshots pulling process completed."


