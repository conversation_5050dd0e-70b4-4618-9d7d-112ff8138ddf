#!/bin/bash

# ShipMate Desktop Installation Script
# This script installs and sets up the ShipMate Desktop application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="ShipMate Desktop"
APP_DIR="/opt/shipmate-desktop"
DESKTOP_FILE="/usr/share/applications/shipmate-desktop.desktop"
ICON_FILE="/usr/share/pixmaps/shipmate-desktop.png"
BIN_FILE="/usr/local/bin/shipmate-desktop"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_error "This script should not be run as root!"
        print_status "Please run as a regular user with sudo privileges."
        exit 1
    fi
}

# Function to check system requirements
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check if we're on a supported system
    if ! command -v apt-get &> /dev/null && ! command -v yum &> /dev/null && ! command -v dnf &> /dev/null; then
        print_error "Unsupported package manager. This installer supports apt (Ubuntu/Debian) and yum/dnf (RHEL/CentOS/Fedora)."
        exit 1
    fi
    
    # Check if Python 3 is available
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed."
        exit 1
    fi
    
    print_success "System requirements check passed"
}

# Function to install system dependencies
install_dependencies() {
    print_status "Installing system dependencies..."
    
    if command -v apt-get &> /dev/null; then
        # Ubuntu/Debian
        sudo apt-get update
        sudo apt-get install -y python3-pip python3-venv python3-dev \
                               git curl wget
        # Try to install PyQt6 via apt, fallback to pip if not available
        if ! sudo apt-get install -y python3-pyqt6 2>/dev/null; then
            print_warning "PyQt6 not available via apt, will install via pip"
        fi
    elif command -v dnf &> /dev/null; then
        # Fedora
        sudo dnf install -y python3-pip python3-virtualenv python3-devel python3-qt6 \
                           python3-qt6-base git curl wget
    elif command -v yum &> /dev/null; then
        # RHEL/CentOS
        sudo yum install -y python3-pip python3-virtualenv python3-devel \
                           git curl wget
        # Note: PyQt6 might need to be installed via pip on older RHEL/CentOS
    fi
    
    print_success "System dependencies installed"
}

# Function to install Python dependencies
install_python_deps() {
    print_status "Installing Python dependencies..."

    # Always try to install PyQt6 via pip if not available
    if ! python3 -c "import PyQt6" &> /dev/null; then
        print_status "Installing PyQt6 via pip..."
        if command -v pip3 &> /dev/null; then
            pip3 install --user PyQt6
        elif command -v pip &> /dev/null; then
            pip install --user PyQt6
        else
            print_error "Neither pip3 nor pip found. Cannot install PyQt6."
            exit 1
        fi

        # Verify installation
        if ! python3 -c "import PyQt6" &> /dev/null; then
            print_error "Failed to install PyQt6. Please install manually:"
            print_info "pip3 install --user PyQt6"
            exit 1
        fi
    fi

    print_success "Python dependencies installed"
}

# Function to create application directory
create_app_directory() {
    print_status "Creating application directory..."
    
    sudo mkdir -p "$APP_DIR"
    sudo cp -r . "$APP_DIR/"
    sudo chown -R root:root "$APP_DIR"
    sudo chmod -R 755 "$APP_DIR"
    sudo chmod +x "$APP_DIR/shipmate_app.py"
    
    print_success "Application files copied to $APP_DIR"
}

# Function to create desktop entry
create_desktop_entry() {
    print_status "Creating desktop entry..."
    
    # Create icon if it exists
    if [[ -f "shipmate-icon.jpg" ]]; then
        sudo cp shipmate-icon.jpg "$ICON_FILE"
    elif [[ -f "icon.png" ]]; then
        sudo cp icon.png "$ICON_FILE"
    else
        print_warning "No icon file found. Desktop entry will use default icon."
    fi
    
    # Create desktop file
    sudo tee "$DESKTOP_FILE" > /dev/null << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=ShipMate Desktop
Comment=Orca AI Fleet Management System
Exec=$BIN_FILE
Icon=$ICON_FILE
Terminal=false
Categories=Utility;Network;
StartupNotify=true
EOF
    
    sudo chmod 644 "$DESKTOP_FILE"
    print_success "Desktop entry created"
}

# Function to create launcher script
create_launcher() {
    print_status "Creating launcher script..."
    
    sudo tee "$BIN_FILE" > /dev/null << EOF
#!/bin/bash
# ShipMate Desktop Launcher

cd "$APP_DIR"
python3 shipmate_app.py "\$@"
EOF
    
    sudo chmod +x "$BIN_FILE"
    print_success "Launcher script created at $BIN_FILE"
}

# Function to update desktop database
update_desktop_database() {
    print_status "Updating desktop database..."
    
    if command -v update-desktop-database &> /dev/null; then
        sudo update-desktop-database /usr/share/applications/
    fi
    
    print_success "Desktop database updated"
}

# Function to create uninstaller
create_uninstaller() {
    print_status "Creating uninstaller..."
    
    sudo tee "$APP_DIR/uninstall.sh" > /dev/null << 'EOF'
#!/bin/bash
# ShipMate Desktop Uninstaller

echo "Uninstalling ShipMate Desktop..."

# Remove application directory
sudo rm -rf /opt/shipmate-desktop

# Remove desktop entry
sudo rm -f /usr/share/applications/shipmate-desktop.desktop

# Remove icon
sudo rm -f /usr/share/pixmaps/shipmate-desktop.png

# Remove launcher
sudo rm -f /usr/local/bin/shipmate-desktop

# Update desktop database
if command -v update-desktop-database &> /dev/null; then
    sudo update-desktop-database /usr/share/applications/
fi

echo "ShipMate Desktop has been uninstalled."
EOF
    
    sudo chmod +x "$APP_DIR/uninstall.sh"
    print_success "Uninstaller created at $APP_DIR/uninstall.sh"
}

# Main installation function
main() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    ShipMate Desktop Installer                ║"
    echo "║                 Orca AI Fleet Management System              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    check_root
    check_requirements
    
    print_status "Starting installation of $APP_NAME..."
    
    install_dependencies
    install_python_deps
    create_app_directory
    create_desktop_entry
    create_launcher
    update_desktop_database
    create_uninstaller
    
    echo
    print_success "Installation completed successfully!"
    echo
    echo -e "${GREEN}You can now run ShipMate Desktop in the following ways:${NC}"
    echo -e "  • From the applications menu: Search for 'ShipMate Desktop'"
    echo -e "  • From the terminal: ${YELLOW}shipmate-desktop${NC}"
    echo -e "  • From the desktop: Look for the ShipMate Desktop icon"
    echo
    echo -e "${BLUE}To uninstall later, run:${NC} sudo $APP_DIR/uninstall.sh"
    echo
}

# Run main function
main "$@"
