# ShipMate Desktop

**Orca AI Fleet Management System**

A powerful desktop application for managing ship fleets, running deployments, monitoring systems, and executing various ship management tasks.

## Features

- 🚢 **Fleet Management**: Select and manage multiple ships simultaneously
- 🚀 **Deployment System**: Deploy applications across your fleet
- 📊 **System Monitoring**: Ping ships, cameras, and VSAT connections
- 📸 **Screenshot Capture**: Take screenshots from ship cameras (day/thermal)
- 🔄 **Docker Management**: Restart Docker containers remotely
- 🧪 **Testing Suite**: Run sanity tests across your fleet
- 📋 **Multi-tab Logging**: Monitor multiple operations simultaneously
- 🔍 **Smart Search**: Find ships and companies with intelligent filtering
- ⚡ **Kill Scripts**: Stop running operations when needed

## System Requirements

- **Operating System**: Linux (Ubuntu 18.04+, Debian 10+, RHEL 8+, CentOS 8+, Fedora 30+)
- **Python**: 3.6 or higher
- **Desktop Environment**: Any modern Linux desktop (GNOME, KDE, XFCE, etc.)
- **Memory**: 512MB RAM minimum, 1GB recommended
- **Storage**: 100MB free space

## Quick Installation

1. **Download or clone the repository**:
   ```bash
   git clone <repository-url>
   cd shipmate-desktop
   ```

2. **Run the installer**:
   ```bash
   ./install.sh
   ```

3. **Launch the application**:
   - From applications menu: Search for "ShipMate Desktop"
   - From terminal: `shipmate-desktop`
   - From desktop: Click the ShipMate Desktop icon

## Manual Installation

If you prefer to install manually or the automatic installer doesn't work:

### Install Dependencies

**Ubuntu/Debian**:
```bash
sudo apt-get update
sudo apt-get install python3-pip python3-pyqt6 python3-pyqt6.qtwidgets
```

**Fedora**:
```bash
sudo dnf install python3-pip python3-qt6
```

**RHEL/CentOS**:
```bash
sudo yum install python3-pip
pip3 install --user PyQt6
```

### Install Application

```bash
# Copy application files
sudo mkdir -p /opt/shipmate-desktop
sudo cp -r . /opt/shipmate-desktop/
sudo chmod +x /opt/shipmate-desktop/shipmate_app.py

# Create launcher
sudo tee /usr/local/bin/shipmate-desktop > /dev/null << 'EOF'
#!/bin/bash
cd /opt/shipmate-desktop
python3 shipmate_app.py "$@"
EOF
sudo chmod +x /usr/local/bin/shipmate-desktop

# Create desktop entry
sudo tee /usr/share/applications/shipmate-desktop.desktop > /dev/null << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=ShipMate Desktop
Comment=Orca AI Fleet Management System
Exec=/usr/local/bin/shipmate-desktop
Terminal=false
Categories=Utility;Network;
StartupNotify=true
EOF
```

## Usage

### Getting Started

1. **Launch the application**
2. **Search for ships**: Use the search bar to find specific ships or companies
3. **Select ships**: Check the boxes next to ships you want to manage
4. **Choose actions**: Click any action button (Deploy, Ping Ships, etc.)
5. **Monitor progress**: Switch to log tabs to see real-time progress
6. **Manage operations**: Use Kill Script to stop operations, Close Tab to clean up

### Main Features

#### Ship Selection
- **Search**: Type ship names or company names (ignores hyphens)
- **Company selection**: Check company boxes to select all ships in that company
- **Individual selection**: Check specific ships for targeted operations

#### Available Actions
- **🚀 Deploy**: Deploy applications to selected ships
- **🔁 Restart Dockers**: Restart Docker containers
- **🧪 Sanity Test**: Run system health checks
- **📸 Screenshot**: Capture current camera views
- **📶 Ping Ships**: Test connectivity to ships
- **🌞 Screenshot Day**: Capture daytime panoramic views
- **🌡 Screenshot Thermal**: Capture thermal camera views
- **📷 Ping Cameras**: Test camera connectivity
- **📡 Ping Vsat**: Test VSAT connectivity
- **➕ Add Ping Slack**: Add ships to Slack monitoring
- **➖ Remove Ping Slack**: Remove ships from Slack monitoring
- **🆕 Pull New Vessels**: Update vessel database

#### Log Management
- **Individual tabs**: Each action creates its own log tab
- **Real-time monitoring**: See live output from running scripts
- **Kill scripts**: Stop operations with the "⚠️ Kill Script" button
- **Close tabs**: Clean up completed operations
- **Close all tabs**: Bulk close all log tabs (appears only when viewing logs)

## Configuration

The application uses configuration files located in the installation directory:
- `hosts`: Contains the list of ships and their connection details
- `selected_version`: Stores the selected deployment version
- `selected_hosts`: Temporary file for currently selected ships

## Troubleshooting

### Common Issues

**Application won't start**:
- Check Python version: `python3 --version` (should be 3.6+)
- Install PyQt6: `pip3 install --user PyQt6`
- Check permissions: `ls -la /opt/shipmate-desktop/`

**No ships showing**:
- Check hosts file exists: `/opt/shipmate-desktop/hosts`
- Verify file permissions and format

**Scripts not running**:
- Check script permissions in `/opt/shipmate-desktop/script/`
- Verify Ansible is installed and configured
- Check network connectivity to ships

**GUI issues**:
- Install Qt6 libraries: `sudo apt-get install python3-pyqt6`
- Try running from terminal to see error messages: `shipmate-desktop`

### Getting Help

If you encounter issues:
1. Run from terminal to see error messages
2. Check log files in the application
3. Verify all dependencies are installed
4. Check network connectivity and permissions

## Uninstallation

To remove ShipMate Desktop:

```bash
sudo /opt/shipmate-desktop/uninstall.sh
```

Or manually:
```bash
sudo rm -rf /opt/shipmate-desktop
sudo rm -f /usr/local/bin/shipmate-desktop
sudo rm -f /usr/share/applications/shipmate-desktop.desktop
sudo rm -f /usr/share/pixmaps/shipmate-desktop.png
```

## License

This software is part of the Orca AI Fleet Management System.

## Support

For technical support and questions, please contact your system administrator or the development team.
