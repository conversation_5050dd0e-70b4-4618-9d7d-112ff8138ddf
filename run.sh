#!/bin/bash

# ShipMate Desktop Launcher Script
# This script can be used to run the application directly from the source directory

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed or not in PATH"
    print_info "Please install Python 3.6 or higher"
    exit 1
fi

# Check Python version
python_version=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
if [[ $(echo "$python_version 3.6" | awk '{print ($1 >= $2)}') -eq 0 ]]; then
    print_error "Python 3.6 or higher is required. Found: $python_version"
    exit 1
fi

# Check if PyQt6 is available
if ! python3 -c "import PyQt6" &> /dev/null; then
    print_error "PyQt6 is not installed"
    print_info "Install it with: pip3 install --user PyQt6"
    print_info "Or on Ubuntu/Debian: sudo apt-get install python3-pyqt6"
    exit 1
fi

# Check if the main application file exists
if [[ ! -f "shipmate_app.py" ]]; then
    print_error "shipmate_app.py not found in current directory"
    print_info "Please run this script from the shipmate-desktop directory"
    exit 1
fi

# Launch the application
print_info "Starting ShipMate Desktop..."
print_success "Application launched successfully!"

# Run the application
python3 shipmate_app.py "$@"
